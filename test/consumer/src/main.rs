//
// main.rs
// pangu HTTP Data Consumer
//

mod shared_memory;
mod data_types;

use clap::{Arg, Command};
use colored::*;
use shared_memory::SharedMemoryConsumer;
use data_types::HttpEvent;
use std::time::Duration;
use tokio::time;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let matches = Command::new("pangu HTTP Data Consumer")
        .version("1.0")
        .author("pangu Team")
        .about("Consumes HTTP parsing results from pangu Upload plugin via shared memory")
        .arg(
            Arg::new("interval")
                .short('i')
                .long("interval")
                .value_name("MILLISECONDS")
                .help("Polling interval in milliseconds")
                .default_value("100")
        )
        .arg(
            Arg::new("detailed")
                .short('d')
                .long("detailed")
                .help("Show detailed information for each event")
                .action(clap::ArgAction::SetTrue)
        )
        .arg(
            Arg::new("quiet")
                .short('q')
                .long("quiet")
                .help("Quiet mode - only show errors")
                .action(clap::ArgAction::SetTrue)
        )
        .arg(
            Arg::new("max-events")
                .short('m')
                .long("max-events")
                .value_name("COUNT")
                .help("Maximum number of events to process (0 = unlimited)")
                .default_value("0")
        )
        .get_matches();

    let interval_ms: u64 = matches.get_one::<String>("interval")
        .unwrap()
        .parse()
        .unwrap_or(100);
    
    let detailed = matches.get_flag("detailed");
    let quiet = matches.get_flag("quiet");
    let max_events: u64 = matches.get_one::<String>("max-events")
        .unwrap()
        .parse()
        .unwrap_or(0);

    if !quiet {
        println!("{}", "🚀 pangu HTTP Data Consumer Starting...".bright_green().bold());
        println!("📊 Polling interval: {}ms", interval_ms);
        println!("📋 Detailed mode: {}", if detailed { "ON" } else { "OFF" });
        println!("🎯 Max events: {}", if max_events == 0 { "Unlimited".to_string() } else { max_events.to_string() });
        println!("{}", "─".repeat(60).bright_blue());
    }

    // 尝试连接到共享内存
    let consumer = loop {
        match SharedMemoryConsumer::new("/pangu_upload_queue") {
            Ok(consumer) => {
                if !quiet {
                    println!("{}", "✅ Connected to shared memory queue".bright_green());
                }
                break consumer;
            }
            Err(e) => {
                if !quiet {
                    println!("{} {}", "⏳ Waiting for shared memory queue...".yellow(), e);
                }
                time::sleep(Duration::from_secs(1)).await;
            }
        }
    };

    let mut event_count = 0u64;
    let mut last_queue_size = 0usize;

    loop {
        let current_queue_size = consumer.size();
        
        // 显示队列状态变化
        if !quiet && current_queue_size != last_queue_size {
            println!("{} Queue size: {}", "📊".bright_blue(), current_queue_size);
            last_queue_size = current_queue_size;
        }

        if let Some(message) = consumer.pop() {
            match serde_json::from_str::<HttpEvent>(&message) {
                Ok(event) => {
                    event_count += 1;
                    
                    if !quiet {
                        println!("\n{} Event #{}", "🔥".bright_red(), event_count);
                        
                        if detailed {
                            println!("{}", event.display_detailed().bright_white());
                        } else {
                            println!("{}", event.display_summary().bright_cyan());
                        }
                        
                        println!("{}", "─".repeat(60).bright_blue());
                    }
                    
                    // 检查是否达到最大事件数
                    if max_events > 0 && event_count >= max_events {
                        if !quiet {
                            println!("{} Reached maximum events limit ({})", "🏁".bright_green(), max_events);
                        }
                        break;
                    }
                }
                Err(e) => {
                    eprintln!("{} Failed to parse JSON: {}", "❌".bright_red(), e);
                    eprintln!("Raw message: {}", message);
                }
            }
        } else {
            // 队列为空，等待一段时间
            time::sleep(Duration::from_millis(interval_ms)).await;
        }
    }

    if !quiet {
        println!("\n{} Consumer finished. Total events processed: {}", 
                 "🎉".bright_green(), event_count);
    }

    Ok(())
}
