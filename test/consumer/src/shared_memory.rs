//
// shared_memory.rs
// Rust implementation of SPSC lock-free queue consumer
//

use std::ffi::CString;
use std::ptr;
use std::sync::atomic::{AtomicUsize, Ordering};
use memmap2::MmapMut;
use libc::{shm_open, O_RDWR, MAP_SHARED, PROT_READ, PROT_WRITE};

// 队列配置（必须与C++端保持一致）
const QUEUE_SIZE: usize = 1024;
const MAX_MESSAGE_SIZE: usize = 4096;
const SHARED_MEMORY_SIZE: usize = std::mem::size_of::<QueueHeader>() + QUEUE_SIZE * (std::mem::size_of::<usize>() + MAX_MESSAGE_SIZE);

// 队列头部结构（与C++端保持一致）
#[repr(C)]
struct QueueHeader {
    producer_index: AtomicUsize,
    consumer_index: AtomicUsize,
}

// 队列条目结构（与C++端保持一致）
#[repr(C)]
struct QueueEntry {
    message_length: usize,
    message_data: [u8; MAX_MESSAGE_SIZE],
}

pub struct SharedMemoryConsumer {
    _mmap: MmapMut,
    header: *mut QueueHeader,
    entries: *mut QueueEntry,
}

unsafe impl Send for SharedMemoryConsumer {}
unsafe impl Sync for SharedMemoryConsumer {}

impl SharedMemoryConsumer {
    pub fn new(name: &str) -> Result<Self, Box<dyn std::error::Error>> {
        // 使用文件映射方式（与C++端保持一致）
        let file_path = format!("/tmp{}.shm", name);

        // 打开共享内存文件
        let c_path = CString::new(file_path.as_str())?;
        let shm_fd = unsafe { libc::open(c_path.as_ptr(), O_RDWR, 0o666) };
        if shm_fd == -1 {
            return Err(format!("Failed to open shared memory file: {}", file_path).into());
        }

        // 映射文件到内存
        let ptr = unsafe {
            libc::mmap(
                ptr::null_mut(),
                SHARED_MEMORY_SIZE,
                PROT_READ | PROT_WRITE,
                MAP_SHARED,
                shm_fd,
                0,
            )
        };

        unsafe { libc::close(shm_fd) };

        if ptr == libc::MAP_FAILED {
            return Err("Failed to map shared memory file".into());
        }

        // 创建一个虚拟的mmap用于生命周期管理
        let _mmap = unsafe { MmapMut::map_anon(0)? };
        
        let header = ptr as *mut QueueHeader;
        let entries = unsafe { 
            (ptr as *mut u8).add(std::mem::size_of::<QueueHeader>()) as *mut QueueEntry 
        };
        
        Ok(SharedMemoryConsumer {
            _mmap,
            header,
            entries,
        })
    }
    
    pub fn pop(&self) -> Option<String> {
        unsafe {
            let current_consumer = (*self.header).consumer_index.load(Ordering::Relaxed);
            let producer_index = (*self.header).producer_index.load(Ordering::Acquire);
            
            // 检查队列是否为空
            if current_consumer == producer_index {
                return None;
            }
            
            // 读取消息
            let entry = &*self.entries.add(current_consumer);
            let message = String::from_utf8_lossy(&entry.message_data[..entry.message_length]).to_string();
            
            // 更新消费者索引
            let next_consumer = (current_consumer + 1) % QUEUE_SIZE;
            (*self.header).consumer_index.store(next_consumer, Ordering::Release);
            
            Some(message)
        }
    }
    
    pub fn is_empty(&self) -> bool {
        unsafe {
            (*self.header).consumer_index.load(Ordering::Acquire) == 
            (*self.header).producer_index.load(Ordering::Acquire)
        }
    }
    
    pub fn size(&self) -> usize {
        unsafe {
            let producer = (*self.header).producer_index.load(Ordering::Acquire);
            let consumer = (*self.header).consumer_index.load(Ordering::Acquire);
            
            if producer >= consumer {
                producer - consumer
            } else {
                QUEUE_SIZE - consumer + producer
            }
        }
    }
}
