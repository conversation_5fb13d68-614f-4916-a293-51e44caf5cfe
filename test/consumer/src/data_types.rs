//
// data_types.rs
// Data structures for HTTP parsing results
//

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Debug, Serialize, Deserialize)]
pub struct HttpInfo {
    pub method: String,
    pub url: String,
    pub path: String,
    pub query: String,
    pub version: String,
    pub status_code: u32,
    pub status_message: String,
    pub content_length: u64,
    pub is_complete: bool,
    #[serde(default)]
    pub headers: String,  // 改为字符串类型，因为C++端发送的是字符串
    #[serde(default)]
    pub body: String,
    #[serde(default)]
    pub body_size: Option<usize>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ConnectionInfo {
    pub src_ip: String,
    pub src_port: u16,
    pub dst_ip: String,
    pub dst_port: u16,
    pub protocol: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProcessingInfo {
    pub parsed_by: String,
    pub uploaded_by: String,
    pub is_valid: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct HttpEvent {
    pub event_type: String,
    pub event_id: String,
    pub timestamp: u64,
    pub capture_time: String,
    pub http: HttpInfo,
    pub connection: ConnectionInfo,
    pub processing: ProcessingInfo,
}

impl HttpEvent {
    pub fn display_summary(&self) -> String {
        format!(
            "[{}] {} {} -> {}:{} | {} | {}",
            self.capture_time,
            self.http.method,
            self.http.url,
            self.connection.dst_ip,
            self.connection.dst_port,
            self.http.status_code,
            if self.http.is_complete { "Complete" } else { "Incomplete" }
        )
    }
    
    pub fn display_detailed(&self) -> String {
        format!(
            "Event ID: {}\n\
             Timestamp: {}\n\
             Capture Time: {}\n\
             HTTP Method: {}\n\
             URL: {}\n\
             Path: {}\n\
             Query: {}\n\
             Version: {}\n\
             Status: {} {}\n\
             Content Length: {}\n\
             Complete: {}\n\
             Source: {}:{}\n\
             Destination: {}:{}\n\
             Protocol: {}\n\
             Parsed by: {}\n\
             Uploaded by: {}\n\
             Valid: {}",
            self.event_id,
            self.timestamp,
            self.capture_time,
            self.http.method,
            self.http.url,
            self.http.path,
            self.http.query,
            self.http.version,
            self.http.status_code,
            self.http.status_message,
            self.http.content_length,
            self.http.is_complete,
            self.connection.src_ip,
            self.connection.src_port,
            self.connection.dst_ip,
            self.connection.dst_port,
            self.connection.protocol,
            self.processing.parsed_by,
            self.processing.uploaded_by,
            self.processing.is_valid
        )
    }
}
