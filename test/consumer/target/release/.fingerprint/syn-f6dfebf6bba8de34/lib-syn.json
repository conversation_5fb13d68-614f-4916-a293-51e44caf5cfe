{"rustc": 15619732123699539844, "features": "[\"clone-impls\", \"default\", \"derive\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 1369601567987815722, "path": 12905022552477250261, "deps": [[1988483478007900009, "unicode_ident", false, 2896840937036158558], [3060637413840920116, "proc_macro2", false, 13077376172028649517], [17990358020177143287, "quote", false, 3567878913535347056]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/syn-f6dfebf6bba8de34/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}