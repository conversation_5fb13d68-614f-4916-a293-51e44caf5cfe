{"rustc": 15619732123699539844, "features": "[\"default\"]", "declared_features": "[\"debug\", \"default\", \"deprecated\", \"raw-deprecated\", \"unstable-markdown\", \"unstable-v5\"]", "target": 905583280159225126, "profile": 12613628788268674035, "path": 17319090616290484545, "deps": [[3060637413840920116, "proc_macro2", false, 13077376172028649517], [4974441333307933176, "syn", false, 9902426706993459906], [13077543566650298139, "heck", false, 9331620565973160450], [17990358020177143287, "quote", false, 3567878913535347056]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/clap_derive-745361cef2373ce6/dep-lib-clap_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}