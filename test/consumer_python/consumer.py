#!/usr/bin/env python3
"""
pangu HTTP Data Consumer (Python Version)
Consumes HTTP parsing results from pangu Upload plugin via shared memory
"""

import mmap
import struct
import json
import time
import argparse
import sys
from typing import Optional, Dict, Any
from dataclasses import dataclass
from pathlib import Path
import os

# 队列配置（必须与C++端保持一致）
QUEUE_SIZE = 1024
MAX_MESSAGE_SIZE = 4096
QUEUE_HEADER_SIZE = 16  # 2 * sizeof(size_t) for atomic indices

@dataclass
class HttpEvent:
    event_type: str
    event_id: str
    timestamp: int
    capture_time: str
    http: Dict[str, Any]
    connection: Dict[str, Any]
    processing: Dict[str, Any]
    
    def display_summary(self) -> str:
        return (f"[{self.capture_time}] {self.http['method']} {self.http['url']} -> "
                f"{self.connection['dst_ip']}:{self.connection['dst_port']} | "
                f"{self.http['status_code']} | "
                f"{'Complete' if self.http['is_complete'] else 'Incomplete'}")
    
    def display_detailed(self) -> str:
        return f"""Event ID: {self.event_id}
Timestamp: {self.timestamp}
Capture Time: {self.capture_time}
HTTP Method: {self.http['method']}
URL: {self.http['url']}
Path: {self.http['path']}
Query: {self.http['query']}
Version: {self.http['version']}
Status: {self.http['status_code']} {self.http['status_message']}
Content Length: {self.http['content_length']}
Complete: {self.http['is_complete']}
Source: {self.connection['src_ip']}:{self.connection['src_port']}
Destination: {self.connection['dst_ip']}:{self.connection['dst_port']}
Protocol: {self.connection['protocol']}
Parsed by: {self.processing['parsed_by']}
Uploaded by: {self.processing['uploaded_by']}
Valid: {self.processing['is_valid']}"""

class SharedMemoryConsumer:
    def __init__(self, name: str):
        self.name = name
        self.shm_path = f"/dev/shm{name}"
        self.fd = None
        self.mmap_obj = None
        self.header_offset = 0
        self.entries_offset = QUEUE_HEADER_SIZE
        
        self._open_shared_memory()
    
    def _open_shared_memory(self):
        """打开共享内存文件"""
        try:
            # 使用文件映射方式的共享内存路径
            self.shm_path = f"/tmp{self.name}.shm"

            # 等待共享内存文件创建
            max_retries = 30
            for i in range(max_retries):
                if os.path.exists(self.shm_path):
                    break
                if i == 0:
                    print(f"⏳ Waiting for shared memory file: {self.shm_path}")
                time.sleep(1)
            else:
                raise FileNotFoundError(f"Shared memory file not found: {self.shm_path}")
            
            self.fd = os.open(self.shm_path, os.O_RDWR)
            self.mmap_obj = mmap.mmap(self.fd, 0, access=mmap.ACCESS_WRITE)
            print(f"✅ Connected to shared memory: {self.shm_path}")
            
        except Exception as e:
            raise RuntimeError(f"Failed to open shared memory: {e}")
    
    def _read_atomic_size_t(self, offset: int) -> int:
        """读取原子size_t值"""
        # 在64位系统上，size_t通常是8字节
        data = self.mmap_obj[offset:offset + 8]
        return struct.unpack('Q', data)[0]  # Q = unsigned long long
    
    def pop(self) -> Optional[str]:
        """从队列中弹出一条消息"""
        if not self.mmap_obj:
            return None
        
        try:
            # 读取消费者和生产者索引
            consumer_index = self._read_atomic_size_t(8)  # consumer_index offset
            producer_index = self._read_atomic_size_t(0)  # producer_index offset
            
            # 检查队列是否为空
            if consumer_index == producer_index:
                return None
            
            # 计算条目偏移
            entry_size = 8 + MAX_MESSAGE_SIZE  # sizeof(size_t) + message_data
            entry_offset = self.entries_offset + consumer_index * entry_size
            
            # 读取消息长度
            msg_len_data = self.mmap_obj[entry_offset:entry_offset + 8]
            message_length = struct.unpack('Q', msg_len_data)[0]
            
            if message_length > MAX_MESSAGE_SIZE:
                print(f"⚠️  Invalid message length: {message_length}")
                return None
            
            # 读取消息数据
            msg_data_offset = entry_offset + 8
            message_data = self.mmap_obj[msg_data_offset:msg_data_offset + message_length]
            message = message_data.decode('utf-8')
            
            # 更新消费者索引
            next_consumer = (consumer_index + 1) % QUEUE_SIZE
            self.mmap_obj[8:16] = struct.pack('Q', next_consumer)
            
            return message
            
        except Exception as e:
            print(f"❌ Error reading from queue: {e}")
            return None
    
    def size(self) -> int:
        """获取队列当前大小"""
        if not self.mmap_obj:
            return 0
        
        try:
            consumer_index = self._read_atomic_size_t(8)
            producer_index = self._read_atomic_size_t(0)
            
            if producer_index >= consumer_index:
                return producer_index - consumer_index
            else:
                return QUEUE_SIZE - consumer_index + producer_index
        except:
            return 0
    
    def close(self):
        """关闭共享内存"""
        if self.mmap_obj:
            self.mmap_obj.close()
        if self.fd:
            os.close(self.fd)

def main():
    parser = argparse.ArgumentParser(description="pangu HTTP Data Consumer (Python)")
    parser.add_argument("-i", "--interval", type=int, default=100,
                       help="Polling interval in milliseconds (default: 100)")
    parser.add_argument("-d", "--detailed", action="store_true",
                       help="Show detailed information for each event")
    parser.add_argument("-q", "--quiet", action="store_true",
                       help="Quiet mode - only show errors")
    parser.add_argument("-m", "--max-events", type=int, default=0,
                       help="Maximum number of events to process (0 = unlimited)")
    
    args = parser.parse_args()
    
    if not args.quiet:
        print("🚀 pangu HTTP Data Consumer Starting... (Python Version)")
        print(f"📊 Polling interval: {args.interval}ms")
        print(f"📋 Detailed mode: {'ON' if args.detailed else 'OFF'}")
        print(f"🎯 Max events: {'Unlimited' if args.max_events == 0 else args.max_events}")
        print("─" * 60)
    
    # 连接到共享内存
    try:
        consumer = SharedMemoryConsumer("/pangu_upload_queue")
    except Exception as e:
        print(f"❌ Failed to connect to shared memory: {e}")
        return 1
    
    event_count = 0
    last_queue_size = 0
    
    try:
        while True:
            current_queue_size = consumer.size()
            
            # 显示队列状态变化
            if not args.quiet and current_queue_size != last_queue_size:
                print(f"📊 Queue size: {current_queue_size}")
                last_queue_size = current_queue_size
            
            message = consumer.pop()
            if message:
                try:
                    event_data = json.loads(message)
                    event = HttpEvent(**event_data)
                    event_count += 1
                    
                    if not args.quiet:
                        print(f"\n🔥 Event #{event_count}")
                        
                        if args.detailed:
                            print(event.display_detailed())
                        else:
                            print(event.display_summary())
                        
                        print("─" * 60)
                    
                    # 检查是否达到最大事件数
                    if args.max_events > 0 and event_count >= args.max_events:
                        if not args.quiet:
                            print(f"🏁 Reached maximum events limit ({args.max_events})")
                        break
                        
                except json.JSONDecodeError as e:
                    print(f"❌ Failed to parse JSON: {e}")
                    print(f"Raw message: {message}")
                except Exception as e:
                    print(f"❌ Error processing event: {e}")
            else:
                # 队列为空，等待一段时间
                time.sleep(args.interval / 1000.0)
                
    except KeyboardInterrupt:
        if not args.quiet:
            print("\n🛑 Consumer interrupted by user")
    finally:
        consumer.close()
        if not args.quiet:
            print(f"\n🎉 Consumer finished. Total events processed: {event_count}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
