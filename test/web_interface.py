import os
import socket
from flask import Flask, request, render_template_string, flash, redirect, url_for

PIPE_PATH = "/tmp/pangoo_ipc.pipe"
PORT = 30001

app = Flask(__name__)
app.secret_key = b'_5#y2L"F4Q8z\n\xec]/'

# 使用字符串列表和 .join() 来定义 HTML，以避免任何解析错误
html_lines = [
    '<!doctype html>',
    '<html lang="en">',
    '<head>',
    '  <meta charset="utf-8">',
    '  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">',
    '  <title>Pangoo Interactor</title>',
    '  <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">',
    '</head>',
    '<body>',
    '  <div class="container mt-5">',
    '    <h1 class="mb-4">Pangoo Control Interface</h1>',
    '    {% with messages = get_flashed_messages(with_categories=true) %}',
    '      {% if messages %}',
    '        {% for category, message in messages %}',
    '          <div class="alert alert-{{ category }}">{{ message }}</div>',
    '        {% endfor %}',
    '      {% endif %}',
    '    {% endwith %}',
    '    <div class="card">',
    '      <div class="card-body">',
    '        <h5 class="card-title">Send Command via Named Pipe</h5>',
    '        <form method="post" action="{{ url_for(\'send_command\') }}">',
    '          <div class="form-group">',
    '            <label for="command">JSON Command:</label>',
    '            <textarea class="form-control" id="command" name="command" rows="5" placeholder=\'e.g., {"msgID": 301, "data": {"type": "get", "target": "status"}}\' >{{ request.form.get(\'command\', \'\') }}</textarea>',
    '          </div>',
    '          <button type="submit" class="btn btn-primary">Send Command</button>',
    '        </form>',
    '      </div>',
    '    </div>',
    '    <div class="mt-4">',
    '        <h5>Example Commands:</h5>',
    '        <ul>',
    '            <li><code>{"msgID": 301, "data": {"type": "get", "target": "status"}}</code> - Get status from Source plugin.</li>',
    '            <li><code>{"msgID": 399, "data": {"type": "get", "target": "telemetry"}}</code> - Request telemetry from all plugins.</li>',
    '        </ul>',
    '    </div>',
    '  </div>',
    '</body>',
    '</html>'
]
HTML_TEMPLATE = '\n'.join(html_lines)

def is_port_in_use(port):
    """检查指定的端口是否已被占用"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) == 0

@app.route('/')
def index():
    return render_template_string(HTML_TEMPLATE)

@app.route('/send_command', methods=['POST'])
def send_command():
    command_str = request.form.get('command')
    if not command_str:
        flash("Command cannot be empty.", "warning")
        return redirect(url_for('index'))

    try:
        if not os.path.exists(PIPE_PATH):
            flash(f"Error: Named pipe \'{PIPE_PATH}\' does not exist. Is the C++ application running?", "danger")
            return redirect(url_for('index'))

        with open(PIPE_PATH, "w") as pipe:
            pipe.write(command_str)
        
        flash(f"Successfully sent command: {command_str}", "success")

    except Exception as e:
        flash(f"An error occurred: {e}", "danger")

    return redirect(url_for('index'))

if __name__ == '__main__':
    if is_port_in_use(PORT):
        print(f"Error: Port {PORT} is already in use by another program.")
        print("Please stop the other program or choose a different port.")
    else:
        print("Starting Pangoo Web Interface...")
        print("Please ensure the C++ application is running to create the named pipe.")
        print(f"Flask server is running on http://127.0.0.1:{PORT}")
        print("To use this, you may need to install Flask: pip3 install Flask")
        app.run(debug=True, host='127.0.0.1', port=PORT, use_reloader=False)
