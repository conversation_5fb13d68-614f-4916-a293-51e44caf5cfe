# Build directories
build/
.build/

# Meson build files
builddir/
.builddir/

# Rust build artifacts
target/
Cargo.lock

# Rust consumer specific
test/consumer/target/
test/consumer/Cargo.lock

# Shared memory files
*.shm
/tmp/*.shm

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store

# Compiled Object files
*.o
*.obj
*.so
*.dylib
*.dll
*.a
*.lib

# Executables
*.exe
*.out
pangoo
/consumer
test/consumer/target/release/consumer

# Debug files
*.dSYM/
*.pdb

# Log files
*.log

# Temporary files
*.tmp
*.temp

# Python cache
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
.env
.venv

# macOS specific
.DS_Store
.AppleDouble
.LSOverride

# Linux specific
*~

# Windows specific
Thumbs.db
ehthumbs.db
Desktop.ini

# Core dumps
core.*

# Backup files
*.bak
*.backup
*~

# Package files
*.tar.gz
*.zip
*.rar
*.7z
