# pangu 测试工具

## 概述

test目录包含三个测试工具，用于验证pangu系统的HTTP数据处理功能。

## 工具列表

### 1. Rust消费者 (`test/consumer/`)

**功能**: 高性能HTTP数据消费者，通过共享内存接收pangu Upload插件的解析结果

**构建**:
```bash
cd test/consumer
./build.sh
# 或者
cargo build --release
```

**使用**:
```bash
# 基本使用
./target/release/consumer

# 详细模式
./target/release/consumer -d

# 自定义轮询间隔
./target/release/consumer -i 50

# 处理指定数量事件
./target/release/consumer -m 100

# 静默模式
./target/release/consumer -q
```

**参数**:
- `-i, --interval <MS>`: 轮询间隔(毫秒) [默认: 100]
- `-d, --detailed`: 显示详细信息
- `-q, --quiet`: 静默模式
- `-m, --max-events <N>`: 最大处理事件数 [默认: 0=无限制]

### 2. Python消费者 (`test/consumer_python/consumer.py`)

**功能**: Python版本的HTTP数据消费者，功能与Rust版本相同

**使用**:
```bash
cd test/consumer_python
python3 consumer.py

# 详细模式
python3 consumer.py -d

# 自定义参数
python3 consumer.py -i 50 -m 100
```

**参数**: 与Rust版本相同

### 3. Web控制界面 (`test/web_interface.py`)

**功能**: 基于Flask的Web界面，用于向pangu系统发送控制命令

**启动**:
```bash
cd test
python3 web_interface.py
```

**访问**: http://localhost:30001

**功能**:
- 通过命名管道发送JSON命令
- 获取插件状态
- 请求遥测数据

**示例命令**:
```json
{"msgID": 301, "data": {"type": "get", "target": "status"}}
{"msgID": 399, "data": {"type": "get", "target": "telemetry"}}
```

## 使用流程

1. **启动pangu系统**
2. **选择消费者工具**:
   - 性能测试: 使用Rust版本
   - 调试开发: 使用Python版本
3. **监控数据**: 消费者会实时显示HTTP事件
4. **控制系统**: 使用Web界面发送命令

## 技术细节

- **共享内存**: `/tmp/pangu_upload_queue.shm`
- **命名管道**: `/tmp/pangu_ipc.pipe`
- **队列大小**: 1024条消息
- **消息大小**: 最大4KB
- **Web端口**: 30001

## 故障排除

**消费者无法连接**:
- 确认pangu Upload插件已启动
- 检查共享内存文件权限

**Web界面无响应**:
- 确认命名管道存在
- 检查端口30001是否被占用

**数据丢失**:
- 降低轮询间隔
- 使用批处理模式
