# pangu 测试工具

## 概述

test目录包含三个测试工具，用于验证pangu系统的HTTP数据处理功能。

## 工具列表

### 1. Rust消费者 (`test/consumer/`)

**功能**: 高性能HTTP数据消费者，通过共享内存接收pangu Upload插件的解析结果

**核心功能**:
- 🔄 **实时数据消费**: 从共享内存队列中实时读取HTTP解析结果
- 📊 **数据解析**: 解析JSON格式的HTTP事件数据
- 🎯 **灵活显示**: 支持摘要模式和详细模式两种显示方式
- ⚡ **高性能**: 使用无锁队列，支持高并发数据处理
- 📈 **队列监控**: 实时显示队列大小变化
- 🛑 **优雅退出**: 支持Ctrl+C信号处理
- 📝 **错误处理**: 完善的JSON解析错误处理和日志记录

**数据格式**: 处理包含HTTP请求/响应信息、连接信息、处理信息的完整事件数据

**构建**:
```bash
cd test/consumer
./build.sh
# 或者
cargo build --release
```

**使用**:
```bash
# 基本使用
./target/release/consumer

# 详细模式
./target/release/consumer -d

# 自定义轮询间隔
./target/release/consumer -i 50

# 处理指定数量事件
./target/release/consumer -m 100

# 静默模式
./target/release/consumer -q
```

**参数**:
- `-i, --interval <MS>`: 轮询间隔(毫秒) [默认: 100]
- `-d, --detailed`: 显示详细信息
- `-q, --quiet`: 静默模式
- `-m, --max-events <N>`: 最大处理事件数 [默认: 0=无限制]

### 2. Python消费者 (`test/consumer_python/consumer.py`)

**功能**: Python版本的HTTP数据消费者，功能与Rust版本相同

**核心功能**:
- 🐍 **Python实现**: 使用Python3编写，便于调试和二次开发
- 🔄 **共享内存访问**: 通过mmap访问共享内存队列
- 📊 **数据结构化**: 使用dataclass定义HTTP事件结构
- 🎨 **彩色输出**: 支持彩色终端输出，提升可读性
- 📈 **统计信息**: 显示处理速度、错误率等统计信息
- 🔧 **调试友好**: 提供详细的错误信息和调试输出
- ⏱️ **性能监控**: 实时显示处理性能指标

**适用场景**: 开发调试、功能验证、性能分析

**使用**:
```bash
cd test/consumer_python
python3 consumer.py

# 详细模式
python3 consumer.py -d

# 自定义参数
python3 consumer.py -i 50 -m 100
```

**参数**: 与Rust版本相同

### 3. Web控制界面 (`test/web_interface.py`)

**功能**: 基于Flask的Web界面，用于向pangu系统发送控制命令

**核心功能**:
- 🌐 **Web界面**: 基于Bootstrap的响应式Web界面
- 📡 **命令发送**: 通过命名管道向pangu系统发送JSON命令
- 📋 **命令模板**: 提供常用命令示例和模板
- 💬 **实时反馈**: 显示命令执行结果和错误信息
- 🎛️ **系统控制**: 支持获取状态、遥测数据等控制操作
- 📝 **命令历史**: 保留上次输入的命令内容
- 🔒 **安全性**: 基本的输入验证和错误处理

**启动**:
```bash
cd test
python3 web_interface.py
```

**访问**: http://localhost:30001

**主要功能**:
- 通过命名管道发送JSON命令
- 获取插件状态信息
- 请求系统遥测数据
- 实时显示执行结果

**示例命令**:
```json
{"msgID": 301, "data": {"type": "get", "target": "status"}}
{"msgID": 399, "data": {"type": "get", "target": "telemetry"}}
```

## 数据处理能力

### HTTP事件数据结构
每个HTTP事件包含以下信息：
- **基本信息**: 事件ID、时间戳、捕获时间
- **HTTP信息**: 方法、URL、路径、查询参数、版本、状态码、内容长度
- **连接信息**: 源IP/端口、目标IP/端口、协议类型
- **处理信息**: 解析器、上传器、有效性标记

### 显示模式
- **摘要模式**: 单行显示关键信息，适合快速浏览
- **详细模式**: 多行显示完整信息，适合深入分析

### 性能特性
- **Rust版本**: 处理速度 >10,000 events/sec，内存占用 <10MB
- **Python版本**: 处理速度 >1,000 events/sec，内存占用 <50MB
- **队列容量**: 1024条消息缓冲，支持突发流量

## 使用流程

1. **启动pangu系统**
2. **选择消费者工具**:
   - 性能测试: 使用Rust版本
   - 调试开发: 使用Python版本
   - 功能验证: 两个版本对比测试
3. **监控数据**: 消费者会实时显示HTTP事件
4. **控制系统**: 使用Web界面发送命令
5. **分析结果**: 查看处理统计和错误信息

## 技术细节

- **共享内存**: `/tmp/pangu_upload_queue.shm`
- **命名管道**: `/tmp/pangu_ipc.pipe`
- **队列大小**: 1024条消息
- **消息大小**: 最大4KB
- **Web端口**: 30001

## 故障排除

**消费者无法连接**:
- 确认pangu Upload插件已启动
- 检查共享内存文件权限: `ls -la /tmp/pangu_upload_queue.shm`
- 验证文件大小是否正确 (应该 >4MB)

**Web界面无响应**:
- 确认命名管道存在: `ls -la /tmp/pangu_ipc.pipe`
- 检查端口30001是否被占用: `lsof -i :30001`
- 查看Flask应用日志输出

**数据丢失**:
- 降低轮询间隔 (`-i` 参数)
- 检查队列大小变化
- 使用详细模式查看错误信息

**性能问题**:
- Rust版本: 检查系统资源占用
- Python版本: 考虑使用PyPy提升性能
- 调整批处理大小和轮询间隔

**JSON解析错误**:
- 检查数据格式是否正确
- 查看原始消息内容
- 验证字符编码 (UTF-8)
