project('pangu', 'c', 'cpp',
  version : '1.0.0',
  default_options : [
    'cpp_std=c++20',
    'warning_level=3',  # 提升到最高告警级别
    'werror=false'      # 暂时保持false，避免编译中断
  ]
)

# 添加编译器参数
add_project_arguments('-pthread', language : 'cpp')

# 根据编译器类型添加特定参数
cpp_compiler = meson.get_compiler('cpp')

# 通用严格编译选项（移除了会产生大量告警的选项）
add_project_arguments([
  '-Wextra',
  '-Wpedantic',
  '-Wshadow',
  '-Wunused',
  # '-Wconversion',           # 移除：会产生大量类型转换告警
  # '-Wsign-conversion',      # 移除：会产生大量符号转换告警
  '-Wcast-align',
  '-Wcast-qual',
  '-Wctor-dtor-privacy',
  '-Wdisabled-optimization',
  '-Wformat=2',
  '-Winit-self',
  '-Wmissing-declarations',
  '-Wmissing-include-dirs',
  # '-Wold-style-cast',       # 移除：C风格转换在某些场景下是必要的
  '-Woverloaded-virtual',
  '-Wredundant-decls',
  '-Wsign-promo',
  '-Wstrict-overflow=5',
  '-Wundef',
  '-Wno-unused-parameter',    # 允许未使用参数（接口需要）
  '-Wno-implicit-int-conversion'  # 允许隐式整数转换
], language : 'cpp')

if cpp_compiler.get_id() == 'clang'
  # Clang特定参数
  add_project_arguments([
    '-U_LIBCPP_ENABLE_ASSERTIONS',  # 取消定义旧的宏
    '-D_LIBCPP_HARDENING_MODE=_LIBCPP_HARDENING_MODE_FAST',
    '-Wno-c++98-compat',
    '-Wno-c++98-compat-pedantic'
  ], language : 'cpp')
elif cpp_compiler.get_id() == 'gcc'
  # GCC特定参数
  add_project_arguments([
    '-Wlogical-op',
    '-Wnoexcept',
    '-Wstrict-null-sentinel'
  ], language : 'cpp')
endif

# 定义包含目录
inc_dirs = include_directories('src')

# 查找依赖
thread_dep = dependency('threads')
dl_dep = meson.get_compiler('cpp').find_library('dl', required : true)
pcap_dep = meson.get_compiler('cpp').find_library('pcap', required : true)

# 定义源文件
common_sources = [
  'src/common/common.h',
  'src/common/msg.h',
  'src/common/interactorMsg.h'
]

core_sources = [
  'src/core/IPlugin.h',
  'src/core/MessageManager.h',
  'src/core/MessageManager.cpp',
  'src/core/ITasker.h',
  'src/core/CWorker.hpp',
  'src/core/Queue.hpp'
]

utils_sources = [
  'src/utils/utils.h'
]

# 主程序
pangu_sources = [
  'src/main.cpp'
]

# 构建主程序
pangu_exe = executable('pangu',
  pangu_sources,
  include_directories : inc_dirs,
  dependencies : [thread_dep, dl_dep, pcap_dep],
  install : false
)

# 构建插件
subdir('src/plugins')

# 获取插件目标（需要在subdir之后）
plugin_targets = [
  get_variable('plugin_manager', disabler()),
  get_variable('plugin_demo_dispatcher', disabler()),
  get_variable('plugin_demo_interactor', disabler()),
  get_variable('plugin_demo_monitor', disabler()),
  get_variable('plugin_demo_parser', disabler()),
  get_variable('plugin_demo_source', disabler()),
  get_variable('plugin_demo_upload', disabler())
]

# 复制配置文件到构建目录
config_files = [
  'config/dispatcher.json',
  'config/interactor.json',
  'config/manager.json',
  'config/monitor.json',
  'config/parser.json',
  'config/source.json',
  'config/upload.json'
]

# 创建config目录并复制配置文件
copy_config = custom_target('copy_config',
  output : 'config_copied.stamp',
  command : [
    'sh', '-c', '''
    mkdir -p @OUTDIR@/config && \
    cp @0@/config/*.json @OUTDIR@/config/ && \
    touch @OUTPUT@
    '''.format(meson.current_source_dir())
  ],
  build_by_default : true
)

# 创建部署目录结构
deploy_dir = meson.current_build_dir() / 'deploy'

# 创建部署目录和复制文件的自定义目标
create_deploy = custom_target('create_deploy',
  output : 'deploy_created.stamp',
  depends : [pangu_exe, copy_config] + plugin_targets,
  command : [
    'sh', '-c', '''
    mkdir -p @0@/config @0@/plugins @0@/3rd @0@/logs @0@/tool
    cp -f @1@ @0@/
    chmod +x @0@/pangu
    cp -rf @2@/config/* @0@/config/
    cp -rf @2@/src/3rd/* @0@/3rd/ 2>/dev/null || true
    # 复制动态库文件（支持Linux和macOS）- 强制覆盖
    cp -f @2@/build/src/plugins/libplugin_*.so @0@/plugins/ 2>/dev/null || true
    cp -f @2@/build/src/plugins/libplugin_*.dylib @0@/plugins/ 2>/dev/null || true
    cd @0@/plugins
    # Linux (.so文件)
    if [ -f libplugin_manager.so ]; then ln -sf libplugin_manager.so plugin_manager.so; fi
    if [ -f libplugin_demo_dispatcher.so ]; then ln -sf libplugin_demo_dispatcher.so plugin_dispatcher.so; fi
    if [ -f libplugin_demo_interactor.so ]; then ln -sf libplugin_demo_interactor.so plugin_interactor.so; fi
    if [ -f libplugin_demo_monitor.so ]; then ln -sf libplugin_demo_monitor.so plugin_monitor.so; fi
    if [ -f libplugin_demo_parser.so ]; then ln -sf libplugin_demo_parser.so plugin_parser.so; fi
    if [ -f libplugin_demo_source.so ]; then ln -sf libplugin_demo_source.so plugin_source.so; fi
    if [ -f libplugin_demo_upload.so ]; then ln -sf libplugin_demo_upload.so plugin_upload.so; fi
    # macOS (.dylib文件)
    if [ -f libplugin_manager.dylib ]; then ln -sf libplugin_manager.dylib plugin_manager.so; fi
    if [ -f libplugin_demo_dispatcher.dylib ]; then ln -sf libplugin_demo_dispatcher.dylib plugin_dispatcher.so; fi
    if [ -f libplugin_demo_interactor.dylib ]; then ln -sf libplugin_demo_interactor.dylib plugin_interactor.so; fi
    if [ -f libplugin_demo_monitor.dylib ]; then ln -sf libplugin_demo_monitor.dylib plugin_monitor.so; fi
    if [ -f libplugin_demo_parser.dylib ]; then ln -sf libplugin_demo_parser.dylib plugin_parser.so; fi
    if [ -f libplugin_demo_source.dylib ]; then ln -sf libplugin_demo_source.dylib plugin_source.so; fi
    if [ -f libplugin_demo_upload.dylib ]; then ln -sf libplugin_demo_upload.dylib plugin_upload.so; fi
    touch @OUTPUT@
    '''.format(deploy_dir, pangu_exe.full_path(), meson.current_source_dir())
  ],
  build_by_default : true
)
