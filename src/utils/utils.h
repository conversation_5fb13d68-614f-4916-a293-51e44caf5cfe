//
//  utils.h
//  pangoo
//
//  Created by <PERSON> on 2023/6/8.
//

#ifndef utils_h
#define utils_h

#ifdef _DEBUG
#define ASSERT(x) assert(x)
#else
#define ASSERT(x) ((void)0)
#endif

#ifndef likely
#define likely(x) __builtin_expect(!!(x), 1)
#endif

#ifndef unlikely
#define unlikely(x) __builtin_expect(!!(x), 0)
#endif

#ifndef MIN
#define MIN(a, b) ((a) < (b) ? (a) : (b))
#endif
#ifndef MAX
#define MAX(a, b) ((a) > (b) ? (a) : (b))
#endif

typedef unsigned int u_int32;
typedef unsigned char u_int8;

#endif /* utils_h */