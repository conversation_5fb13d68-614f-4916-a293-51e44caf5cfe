//
//  ShmQueue.hpp
//  pangu
//
//  SPSC Lock-free Queue in Shared Memory
//  Pure C++ Header-Only Implementation

//  Created by <PERSON> on 2023/6/9.
//

#ifndef SHM_QUEUE_HPP
#define SHM_QUEUE_HPP

#include <sys/mman.h>    // mmap, munmap, MAP_SHARED, PROT_READ, PROT_WRITE
#include <fcntl.h>       // open, O_CREAT, O_RDWR
#include <unistd.h>      // close, ftruncate, unlink
#include <cstring>       // std::memcpy
#include <string>        // std::string
#include <stdexcept>     // std::runtime_error

// 队列配置
constexpr size_t QUEUE_SIZE = 1024;                    // 队列容量（条目数）
constexpr size_t MAX_MESSAGE_SIZE = 4096;              // 单条消息最大大小
constexpr size_t SHARED_MEMORY_SIZE = sizeof(size_t) * 2 + QUEUE_SIZE * (sizeof(size_t) + MAX_MESSAGE_SIZE);

// 共享内存中的队列头部结构
struct QueueHeader {
    volatile size_t producer_index{0};     // 生产者索引
    volatile size_t consumer_index{0};     // 消费者索引
};

// 队列条目结构
struct QueueEntry {
    size_t message_length;                 // 消息长度
    char message_data[MAX_MESSAGE_SIZE];   // 消息数据
};

// ============================================================================
// Pure C++ Implementation
// ============================================================================

class ShmQueue {
private:
    std::string m_name;
    int m_shm_fd;
    void* m_shm_ptr;
    QueueHeader* m_header;
    QueueEntry* m_entries;
    bool m_is_producer;
    bool m_initialized;

    // 内部辅助函数
    static size_t next_index(size_t current) {
        return (current + 1) % QUEUE_SIZE;
    }

    static void memory_barrier_acquire() {
        __asm__ __volatile__("" ::: "memory");
    }

    static void memory_barrier_release() {
        __asm__ __volatile__("" ::: "memory");
    }

    bool initialize_shared_memory() {
        // 使用文件映射方式（与macOS兼容）
        std::string file_path = "/tmp" + m_name + ".shm";
        
        if (m_is_producer) {
            // 生产者：创建文件
            m_shm_fd = open(file_path.c_str(), O_CREAT | O_RDWR, 0666);
            if (m_shm_fd == -1) {
                return false;
            }
            
            // 设置文件大小
            if (ftruncate(m_shm_fd, SHARED_MEMORY_SIZE) == -1) {
                close(m_shm_fd);
                unlink(file_path.c_str());
                return false;
            }
        } else {
            // 消费者：打开现有文件
            m_shm_fd = open(file_path.c_str(), O_RDWR, 0666);
            if (m_shm_fd == -1) {
                return false;
            }
        }
        
        // 映射文件到内存
        m_shm_ptr = mmap(nullptr, SHARED_MEMORY_SIZE, PROT_READ | PROT_WRITE, MAP_SHARED, m_shm_fd, 0);
        if (m_shm_ptr == MAP_FAILED) {
            close(m_shm_fd);
            if (m_is_producer) {
                unlink(file_path.c_str());
            }
            return false;
        }
        
        // 设置指针
        m_header = static_cast<QueueHeader*>(m_shm_ptr);
        m_entries = reinterpret_cast<QueueEntry*>(static_cast<char*>(m_shm_ptr) + sizeof(QueueHeader));
        
        // 如果是生产者，初始化队列头部
        if (m_is_producer) {
            m_header->producer_index = 0;
            m_header->consumer_index = 0;
        }
        
        return true;
    }

    void cleanup_shared_memory() {
        if (m_is_producer) {
            std::string file_path = "/tmp" + m_name + ".shm";
            unlink(file_path.c_str());
        }
    }

public:
    // 构造函数：创建或打开共享内存
    ShmQueue(const std::string& name, bool is_producer = true) 
        : m_name(name), m_shm_fd(-1), m_shm_ptr(nullptr), 
          m_header(nullptr), m_entries(nullptr), 
          m_is_producer(is_producer), m_initialized(false) {
        
        if (initialize_shared_memory()) {
            m_initialized = true;
        } else {
            throw std::runtime_error("Failed to initialize shared memory queue: " + name);
        }
    }
    
    // 析构函数：清理资源
    ~ShmQueue() {
        if (m_shm_ptr != nullptr) {
            munmap(m_shm_ptr, SHARED_MEMORY_SIZE);
        }
        
        if (m_shm_fd != -1) {
            close(m_shm_fd);
        }
        
        // 只有生产者负责清理共享内存
        if (m_is_producer) {
            cleanup_shared_memory();
        }
    }
    
    // 禁用拷贝构造和赋值
    ShmQueue(const ShmQueue&) = delete;
    ShmQueue& operator=(const ShmQueue&) = delete;

    // 生产者接口：写入消息
    bool push(const std::string& message) {
        if (!m_initialized || !m_is_producer) {
            return false;
        }

        if (message.length() > MAX_MESSAGE_SIZE) {
            return false; // 消息太大
        }

        size_t current_producer = m_header->producer_index;
        size_t next_producer = next_index(current_producer);

        // 检查队列是否已满
        memory_barrier_acquire();
        if (next_producer == m_header->consumer_index) {
            return false; // 队列已满
        }

        // 写入消息
        QueueEntry* entry = &m_entries[current_producer];
        entry->message_length = message.length();
        std::memcpy(entry->message_data, message.c_str(), message.length());

        // 更新生产者索引
        memory_barrier_release();
        m_header->producer_index = next_producer;

        return true;
    }

    // 消费者接口：读取消息
    bool pop(std::string& message) {
        if (!m_initialized || m_is_producer) {
            return false;
        }

        size_t current_consumer = m_header->consumer_index;

        // 检查队列是否为空
        memory_barrier_acquire();
        if (current_consumer == m_header->producer_index) {
            return false; // 队列为空
        }

        // 读取消息
        QueueEntry* entry = &m_entries[current_consumer];
        size_t msg_len = entry->message_length;

        message.assign(entry->message_data, msg_len);

        // 更新消费者索引
        size_t next_consumer = next_index(current_consumer);
        memory_barrier_release();
        m_header->consumer_index = next_consumer;

        return true;
    }

    // 检查队列是否为空
    bool empty() const {
        if (!m_initialized) {
            return true;
        }

        memory_barrier_acquire();
        return m_header->consumer_index == m_header->producer_index;
    }

    // 检查队列是否已满
    bool full() const {
        if (!m_initialized) {
            return false;
        }

        size_t current_producer = m_header->producer_index;
        size_t next_producer = next_index(current_producer);

        memory_barrier_acquire();
        return next_producer == m_header->consumer_index;
    }

    // 获取队列大小
    size_t size() const {
        if (!m_initialized) {
            return 0;
        }

        memory_barrier_acquire();
        size_t producer = m_header->producer_index;
        size_t consumer = m_header->consumer_index;

        if (producer >= consumer) {
            return producer - consumer;
        } else {
            return QUEUE_SIZE - consumer + producer;
        }
    }

    // 获取队列容量
    static constexpr size_t capacity() {
        return QUEUE_SIZE;
    }
};

#endif // SHM_QUEUE_HPP
