//
//  IPlugin.hpp
//  pangoo
//
//  Created by <PERSON> on 2023/6/30.
//

#ifndef IPlugin_h
#define IPlugin_h

#include "common/common.h"
#include "common/msg.h"
#include "utils/utils.h"
#include "3rd/json/json.hpp"
#include "core/Queue.hpp"
#include "core/CWorker.hpp"
#include <vector>

// 前向声明
class ITasker;
class IPlugin;
#include <map>

using namespace std;
using json = nlohmann::json;

struct PluginInfo
{
    string _name;
    string _module;
    string _description;

    // 数据流消息ID
    vector<uint32_t> _dataflow_input;
    vector<uint32_t> _dataflow_output;

    // 控制流消息ID
    vector<uint32_t> _controlflow_receive;

    uint32_t _workerNum;

    PluginInfo(string path)
    {
        std::ifstream f(path.c_str(), ios::in);
        json conf = json::parse(f);
        f.close();

        _name = conf["name"];
        _module = conf["module"];
        _description = conf["description"];
        _workerNum = conf["workerNum"];

        if (conf.contains("dataflow"))
        {
            if (conf["dataflow"].contains("input"))
            {
                parseMessageIds(conf["dataflow"]["input"], _dataflow_input);
            }
            if (conf["dataflow"].contains("output"))
            {
                parseMessageIds(conf["dataflow"]["output"], _dataflow_output);
            }
        }

        if (conf.contains("controlflow") && conf["controlflow"].is_array())
        {
            for (int element : conf["controlflow"])
            {
                _controlflow_receive.push_back(element);
            }
        }
    }

    ~PluginInfo()
    {
        printf("free PluginInfo \n");
    }

   private:
    void parseMessageIds(const json& value, vector<uint32_t>& target)
    {
        if (value.is_array()) { for (int element : value) { target.push_back(element); } }
        else if (value.is_string()) { string str = value; if (!str.empty()) { target.push_back(std::stoi(str)); } }
        else if (value.is_number()) { target.push_back(value); }
    }
};

class IPlugin
{
   public:
    IPlugin() : m_worker(nullptr)
    {
        m_queue = new Queue();
        m_downstream_queue = nullptr;
        m_worker = new CWorker();
    }

    virtual ~IPlugin()
    {
        if (m_worker) { delete m_worker; m_worker = nullptr; }
        if (m_queue) { delete m_queue; m_queue = nullptr; }
        if (m_tasker) { delete m_tasker; m_tasker = nullptr; }
        printf("插件析构完成\n");
    }

    virtual PluginInfo* info() = 0;

    // 新的 telemetry 接口，返回一个 JSON 对象
    virtual json telemetry()
    {
        // 默认实现：返回一个包含基本状态的JSON
        return {
            {"plugin_name", info()->_name},
            {"status", "not_implemented"}
        };
    }

    // 新的 control 接口，接收一个 JSON 对象
    virtual void control(int msgID, const json& controlData)
    {
        // 默认实现：打印收到的控制命令，子类可以重写
        printf("Plugin '%s' received control msgID %d with data: %s\n",
               info()->_name.c_str(), msgID, controlData.dump().c_str());
    }

    virtual void init() = 0;
    virtual void uninit() = 0;

    virtual void start()
    {
        if (m_worker) { m_worker->start(); }
    }

    virtual void stop()
    {
        if (m_worker) { m_worker->stop(); }
    }

    Queue* getQueue() { return m_queue; }

    void setup(Queue* downstreamQueue)
    {
        m_downstream_queue = downstreamQueue;
        if (m_downstream_queue)
        {
            m_worker->setup(m_queue, m_downstream_queue);
            LOG_INFO("Worker 队列设置完成: queue=" << m_queue << ", downstream=" << m_downstream_queue);
        }
        else
        {
            m_worker->setup(m_queue, nullptr);
            LOG_INFO("Worker 队列设置完成（终端插件): queue=" << m_queue << ", downstream=nullptr");
        }

        if (m_tasker)
        {
            m_worker->setup(m_tasker);
            LOG_INFO("Worker Tasker 设置完成: " << m_tasker);
        }
        else
            LOG_INFO("m_tasker 没有创建成功");
    }

   protected:
    Queue* m_queue = nullptr;
    Queue* m_downstream_queue = nullptr;
    CWorker* m_worker = nullptr;
    PluginInfo* m_info;
    ITasker* m_tasker = nullptr;
};

#endif /* IPlugin_h */
