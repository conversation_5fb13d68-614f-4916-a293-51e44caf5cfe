//
//  Queue.hpp
//  pangoo
//
//  Created by <PERSON> on 2023/6/9.
//

#ifndef Queue_hpp
#define Queue_hpp

#include "common/common.h"
#include "utils/utils.h"

#define QUEUE_OK 0
#define QUEUE_NO_ELEMENT -1
#define QUEUE_FULL -2

#define spsc_compiler_barrier() __asm__ __volatile__("" ::: "memory")
#define spsc_write_release() __asm__ __volatile__("" ::: "memory")
#define spsc_read_acquire() __asm__ __volatile__("" ::: "memory")

const u_int8 QUEUE_MAX_NUM = 255;
const u_int8 QUEUE_MAX_INDEX = 254;

struct QueueCursor
{
    volatile u_int8 head;
    volatile u_int8 tail;

    QueueCursor() : head(0), tail(0)
    {
    }
};

class Queue
{
   public:
    Queue() : m_u8_queue_num(QUEUE_MAX_NUM), m_u8_max_index(QUEUE_MAX_INDEX) {};
    ~Queue() {};

    int produce(void* val)
    {
        int n = move_pord_head(1);
        if (n < 0)
        {
            printf("Queue::produce - 队列满了，无法添加消息\n");
            return n;
        }

        enqueue(val);

        update_prod_tail();

        // printf("Queue::produce - 成功添加消息 %p，队列元素数量: %d\n", val, elements());

        return QUEUE_OK;
    };

    int consume(void** val)
    {
        int n = move_cons_head(1);
        if (n < 0)
        {
            // printf("Queue::consume - 队列为空，无消息可取\n");
            return n;
        }

        dequeue(val);
        update_cons_tail();

        // printf("Queue::consume - 成功获取消息 %p，剩余元素数量: %d\n", *val, elements());

        return QUEUE_OK;
    };

    int elements()
    {
        u_int8 count = (m_u8_queue_num + m_prod.tail - m_cons.head) % m_u8_queue_num;
        return count;
    };

    int flush()
    {
        return 0;
    };

   private:
    void enqueue(void* val)
    {
        if (likely(m_prod.tail <= QUEUE_MAX_INDEX))
        {
            data[m_prod.tail] = val;
        }
        else
        {
            data[0] = val;
        }

        return;
    };

    void dequeue(void** val)
    {
        if (likely(m_cons.tail <= QUEUE_MAX_INDEX))
        {
            *val = data[m_cons.tail];
        }
        else
        {
            *val = data[0];
        }

        return;
    };

    int move_pord_head(u_int8 num)
    {
        u_int8 entries = 0;
        u_int8 n = num;

        spsc_read_acquire();
        entries = (m_u8_max_index + m_cons.tail - m_prod.head) % m_u8_queue_num;
        if (entries == 0)
        {
            return QUEUE_FULL;
        }

        if (unlikely(num > entries))
        {
            n = entries;
        }

        m_prod.head = (m_prod.head + n) % m_u8_queue_num;

        return n;
    };

    void update_prod_tail()
    {
        spsc_write_release();
        m_prod.tail = m_prod.head;
        return;
    };

    int move_cons_head(int num)
    {
        u_int8 entries = 0;
        u_int8 n = num;

        spsc_read_acquire();
        entries = (m_u8_queue_num + m_prod.tail - m_cons.head) % m_u8_queue_num;
        if (entries == 0)
        {
            return QUEUE_NO_ELEMENT;
        }

        if (unlikely(n > entries))
        {
            n = entries;
        }

        m_cons.head = (m_cons.head + n) % m_u8_queue_num;

        return n;
    };

    void update_cons_tail()
    {
        spsc_write_release();
        m_cons.tail = m_cons.head;
        return;
    };

   private:
    void* data[QUEUE_MAX_NUM];
    u_int8 m_u8_queue_num;
    u_int8 m_u8_max_index;
    QueueCursor m_prod;
    QueueCursor m_cons;
};

#endif /* Queue_hpp */