//
//  CSourcePlugin.h
//  nuwa
//
//  Created by <PERSON> on 2023/11/6.
//  Redesigned for framework compliance
//

#ifndef CSourcePlugin_hpp
#define CSourcePlugin_hpp

#include "common/common.h"
#include "common/msg.h"
#include "core/IPlugin.h"
#include "CSourceTasker.h"
#include <memory>
#include <thread>
#include <atomic>

class CSourcePlugin : public IPlugin
{
public:
    CSourcePlugin();
    virtual ~CSourcePlugin();

    virtual PluginInfo* info() override;

    // 实现新的 control 和 telemetry 接口
    virtual void control(int msgID, const json& controlData) override;
    virtual json telemetry() override;

    virtual void init() override;
    virtual void uninit() override;
    virtual void start() override;
    virtual void stop() override;

private:
    std::thread m_capture_thread;
    std::atomic<bool> m_isRunning;

    void packet_capture_loop();
};

#endif /* CSourcePlugin_hpp */
