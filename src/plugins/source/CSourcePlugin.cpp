//
//  CSourcePlugin.cpp
//  pangu
//
//  Created by <PERSON> on 2023/11/6.
//  Redesigned for framework compliance
//

#include "CSourcePlugin.h"
#include "3rd/json/json.hpp"
#include <fstream>

using json = nlohmann::json;

CSourcePlugin::CSourcePlugin()
    : m_isRunning(false)
{
    m_info = new PluginInfo("./config/source.json");
    LOG_INFO("CSourcePlugin 构造完成");
}

CSourcePlugin::~CSourcePlugin()
{
    LOG_INFO("CSourcePlugin 开始析构");
    stop();
    if (m_capture_thread.joinable()) {
        m_capture_thread.join();
    }
    if (m_info) {
        delete m_info;
        m_info = nullptr;
    }
    LOG_INFO("CSourcePlugin 析构完成");
}

PluginInfo* CSourcePlugin::info()
{
    return m_info;
}

// 实现新的 control 接口
void CSourcePlugin::control(int msgID, const json& controlData)
{
    LOG_INFO("Source 插件收到控制消息 (msgID: " << msgID << "): " << controlData.dump());

    try {
        string type = controlData.value("type", "");

        if (type == "set") {
            string filter = controlData.value("filter", "");
            if (!filter.empty() && m_tasker) {
                // 动态更新过滤器需要 CSourceTasker 提供一个接口
                // 例如: ((CSourceTasker*)m_tasker)->setFilter(filter);
                LOG_INFO("请求设置新的抓包过滤器: " << filter);
            }
        } else if (type == "get") {
            string target = controlData.value("target", "");
            if (target == "status") {
                LOG_INFO("被请求获取状态, 当前状态: " << (m_isRunning ? "运行中" : "已停止"));
            }
        } else {
            LOG_WARN("未知的控制类型: " << type);
        }
    } catch (const json::exception& e) {
        LOG_ERROR("解析控制消息JSON失败: " << e.what());
    }
}

// 实现新的 telemetry 接口
json CSourcePlugin::telemetry()
{
    if (m_tasker) {
        // 假设 CSourceTasker 有一个 getStats() 方法返回JSON
        // return ((CSourceTasker*)m_tasker)->getStats();
        return {
            {"plugin_name", "source"},
            {"status", m_isRunning ? "running" : "stopped"},
            {"info", "stats_not_implemented_in_tasker"}
        };
    }
    return {
        {"plugin_name", "source"},
        {"status", m_isRunning ? "running" : "stopped"}
    };
}

void CSourcePlugin::init()
{
    LOG_INFO("CSourcePlugin::init() - 开始初始化");
    m_tasker = new CSourceTasker();
    m_isRunning = true;
    try {
        m_capture_thread = std::thread(&CSourcePlugin::packet_capture_loop, this);
        LOG_INFO("数据捕获线程创建成功");
    } catch (const std::exception& e) {
        LOG_ERROR("创建数据包捕获线程失败: " << e.what());
        m_isRunning = false;
    }
    LOG_INFO("CSourcePlugin初始化完成");
}

void CSourcePlugin::uninit()
{
    LOG_INFO("CSourcePlugin::uninit() - 开始清理");
    m_isRunning = false;
    if (m_capture_thread.joinable()) {
        m_capture_thread.join();
        LOG_INFO("数据捕获线程已停止");
    }
    LOG_INFO("CSourcePlugin清理完成");
}

void CSourcePlugin::start()
{
    LOG_INFO("CSourcePlugin::start() - 启动插件");
    if (m_worker) {
        m_worker->stop();
        LOG_INFO("Source插件已停止Worker（不需要消息处理）");
    }
    if (!m_isRunning) {
        LOG_ERROR("数据捕获线程未运行");
    }
}

void CSourcePlugin::stop()
{
    LOG_INFO("CSourcePlugin::stop() - 停止插件");
    m_isRunning = false;
    if (m_worker) {
        m_worker->stop();
    }
}

void CSourcePlugin::packet_capture_loop()
{
    LOG_INFO("数据包捕获循环开始");
    while (m_isRunning) {
        if (m_tasker) {
            void* result = m_tasker->execute(nullptr);
            if (result) {
                if (m_downstream_queue) {
                    if (m_downstream_queue->produce(result) != QUEUE_OK) {
                        LOG_WARN("推送数据到下游队列失败");
                        delete (NetworkPacketData*)result;
                    }
                } else {
                    delete (NetworkPacketData*)result;
                }
            }
        }
        usleep(1000);
    }
    LOG_INFO("数据包捕获循环结束");
}