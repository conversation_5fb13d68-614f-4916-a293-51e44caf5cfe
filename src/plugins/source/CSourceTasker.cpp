//
//  CSourceTasker.cpp
//  pangu
//
//  Created by <PERSON> on 2023/6/30.
//  Redesigned for framework compliance
//

#include "CSourceTasker.h"
#include "CSourcePlugin.h"

#include "3rd/json/json.hpp"
#include <sstream>
#include <iostream>
#include <cstring>
#include <iomanip>
#include <fstream>

using json = nlohmann::json;

CSourceTasker::CSourceTasker()
    : m_handle(nullptr), m_isCapturing(false),
      m_totalPackets(0), m_validPackets(0), m_captureInterval(1), m_batchSize(5)
{
    // 初始化统计信息
    m_stats.packetsReceived = 0;
    m_stats.packetsDropped = 0;
    m_stats.packetsTotal = 0;

    // 加载配置
    loadConfiguration();

    // 初始化数据包捕获器
    if (!initializeCapture()) {
        LOG_ERROR("CSourceTasker初始化失败");
    } else {
        LOG_INFO("CSourceTasker初始化成功");
    }
}

CSourceTasker::~CSourceTasker()
{
    LOG_INFO("CSourceTasker开始析构");
    stopCapture();
    cleanup();
    LOG_INFO("CSourceTasker析构完成");
}

// void* CSourceTasker::init(json* conf)
// {
//     // 初始化配置（如果需要）
//     (void)conf;
//     return nullptr;
// }

void* CSourceTasker::execute(void* msg)
{
    // 作为回调处理器，被Plugin的线程调用
    (void)msg;  // 忽略输入消息

    // 尝试捕获单个数据包
    NetworkPacketData* packet = getNextPacket();
    if (!packet) {
        return nullptr;
    }

    // 预处理数据包
    if (!preprocessPacket(packet)) {
        delete packet;
        return nullptr;
    }

    m_validPackets++;
    m_totalPackets++;

    // 简单过滤：只关注TCP协议的HTTP/HTTPS端口
    if (packet->protocol == "TCP" &&
        (packet->dstPort == 80 || packet->srcPort == 80 ||
         packet->dstPort == 443 || packet->srcPort == 443)) {

        return packet;  // 返回给Plugin推送到下游Parser插件
    } else {
        // 非HTTP/HTTPS数据包，直接丢弃
        delete packet;
        return nullptr;
    }
}

void CSourceTasker::clear()
{
    LOG_INFO("CSourceTasker::clear() - 清理任务状态");
    m_totalPackets = 0;
    m_validPackets = 0;
}



bool CSourceTasker::initializeCapture()
{
    // 初始化设备
    if (!initDevice(m_deviceName, m_filterExpression)) {
        LOG_ERROR("初始化网卡设备失败: " << m_lastError);
        return false;
    }

    // 开始捕获
    if (!startCapture()) {
        LOG_ERROR("启动网卡抓包失败: " << m_lastError);
        return false;
    }

    LOG_INFO("数据包捕获器初始化成功 - 设备:" << m_deviceName << ", 过滤器:" << m_filterExpression);
    return true;
}





bool CSourceTasker::preprocessPacket(NetworkPacketData* packet)
{
    if (!packet) {
        return false;
    }

    // 基本有效性检查
    if (packet->packetSize == 0 || packet->packetSize > 65536) {
        LOG_WARN("数据包大小异常: " << packet->packetSize);
        return false;
    }

    if (!packet->packetData) {
        LOG_WARN("数据包内容为空");
        return false;
    }

    // 可以在这里添加更多的过滤逻辑
    // 例如：只处理特定协议的数据包

    return true;
}

void CSourceTasker::cleanup()
{
    stopCapture();
    LOG_INFO("CSourceTasker清理完成 - 总包数:" << m_totalPackets << ", 有效包数:" << m_validPackets);
}

void CSourceTasker::loadConfiguration()
{
    try {
        // 读取配置文件
        std::ifstream configFile("./config/source.json");
        if (!configFile.is_open()) {
            LOG_WARN("无法打开配置文件，使用默认配置");
            m_deviceName = "en0";
            m_filterExpression = "tcp or udp";
            m_captureInterval = 1;
            m_batchSize = 5;
            return;
        }

        json config;
        configFile >> config;
        configFile.close();

        // 解析配置
        if (config.contains("capture")) {
            auto capture = config["capture"];
            m_deviceName = capture.value("device", "en0");
            m_filterExpression = capture.value("filter", "tcp or udp");
            m_captureInterval = capture.value("interval", 1);
            m_batchSize = capture.value("batch_size", 5);

            // 配置验证
            if (m_captureInterval < 1) {
                LOG_WARN("捕获间隔过小，调整为1ms");
                m_captureInterval = 1;
            }
            if (m_batchSize < 1) {
                LOG_WARN("批处理大小过小，调整为1");
                m_batchSize = 1;
            }
        } else {
            LOG_WARN("未找到capture配置节，使用默认配置");
        }

        LOG_INFO("Tasker配置加载完成 - 设备:" << m_deviceName << ", 间隔:" << m_captureInterval << "ms");

    } catch (const exception& e) {
        LOG_ERROR("读取配置文件失败: " << e.what() << ", 使用默认配置");
        m_deviceName = "en0";
        m_filterExpression = "tcp or udp";
        m_captureInterval = 1;
        m_batchSize = 5;
    }
}

// PacketCapture功能实现（迁移过来）
bool CSourceTasker::initDevice(const string& deviceName, const string& filter)
{
    char errbuf[PCAP_ERRBUF_SIZE];

    // 如果没有指定设备名，自动选择默认设备
    if (deviceName.empty()) {
        vector<string> devices = getAvailableDevices();
        if (devices.empty()) {
            m_lastError = "无法找到可用的网卡设备";
            return false;
        }
        m_deviceName = devices[0];  // 使用第一个可用设备
    } else {
        m_deviceName = deviceName;
    }

    // 打开网卡设备
    LOG_INFO("尝试打开网卡设备 " << m_deviceName);
    // 将超时从1000ms减少到10ms，减少延迟
    m_handle = pcap_open_live(m_deviceName.c_str(), BUFSIZ, 1, 10, errbuf);
    if (m_handle == nullptr) {
        m_lastError = "无法打开网卡设备 " + m_deviceName + ": " + string(errbuf);
        LOG_ERROR(m_lastError);
        LOG_ERROR("提示 - 在 macOS 上可能需要管理员权限 (sudo)");
        return false;
    }

    LOG_INFO("成功打开网卡设备 " << m_deviceName);

    // 设置过滤器
    if (!filter.empty()) {
        if (!setFilter(filter)) {
            pcap_close(m_handle);
            m_handle = nullptr;
            return false;
        }
    }

    return true;
}

bool CSourceTasker::startCapture()
{
    if (!m_handle) {
        m_lastError = "设备未初始化";
        return false;
    }

    m_isCapturing = true;
    LOG_INFO("开始抓包...");
    return true;
}

void CSourceTasker::stopCapture()
{
    m_isCapturing = false;
    if (m_handle) {
        pcap_close(m_handle);
        m_handle = nullptr;
        LOG_INFO("停止抓包");
    }
}

NetworkPacketData* CSourceTasker::getNextPacket()
{
    if (!m_handle || !m_isCapturing) {
        return nullptr;
    }

    struct pcap_pkthdr* header;
    const u_char* packet;

    int result = pcap_next_ex(m_handle, &header, &packet);
    if (result == 1) {
        // 成功捕获数据包
        NetworkPacketData* packetData = new NetworkPacketData();

        // 设置基本信息
        packetData->packetSize = header->len;
        packetData->timestamp = header->ts.tv_sec;

        // 分配内存并复制数据包内容
        packetData->packetData = malloc(header->len);
        memcpy(packetData->packetData, packet, header->len);

        // 解析数据包头部
        if (parseEthernetHeader(packet, packetData)) {
            m_stats.packetsReceived++;
            return packetData;
        } else {
            delete packetData;
            return nullptr;
        }
    } else if (result == -1) {
        m_lastError = "抓包错误: " + string(pcap_geterr(m_handle));
        LOG_ERROR(m_lastError);
        return nullptr;
    }

    // result == 0 表示超时，没有数据包
    return nullptr;
}

vector<string> CSourceTasker::getAvailableDevices()
{
    vector<string> devices;
    char errbuf[PCAP_ERRBUF_SIZE];
    pcap_if_t* alldevs;

    if (pcap_findalldevs(&alldevs, errbuf) == -1) {
        LOG_ERROR("查找网卡设备失败: " << errbuf);
        return devices;
    }

    for (pcap_if_t* dev = alldevs; dev != nullptr; dev = dev->next) {
        devices.push_back(dev->name);
    }

    pcap_freealldevs(alldevs);
    return devices;
}

bool CSourceTasker::setFilter(const string& filterExpression)
{
    if (!m_handle) {
        m_lastError = "设备未初始化";
        return false;
    }

    struct bpf_program fp;
    if (pcap_compile(m_handle, &fp, filterExpression.c_str(), 0, PCAP_NETMASK_UNKNOWN) == -1) {
        m_lastError = "编译过滤器失败: " + string(pcap_geterr(m_handle));
        return false;
    }

    if (pcap_setfilter(m_handle, &fp) == -1) {
        m_lastError = "设置过滤器失败: " + string(pcap_geterr(m_handle));
        pcap_freecode(&fp);
        return false;
    }

    pcap_freecode(&fp);
    m_filterExpression = filterExpression;
    LOG_INFO("设置过滤器: " << filterExpression);

    return true;
}

// 数据包解析方法（从PacketCapture迁移）
bool CSourceTasker::parseEthernetHeader(const u_char* packet, NetworkPacketData* packetData)
{
    // 使用memcpy安全地读取以太网头部，避免内存对齐问题
    struct ether_header eth_header;
    memcpy(&eth_header, packet, sizeof(struct ether_header));

    // 解析MAC地址
    packetData->srcMAC = macToString(eth_header.ether_shost);
    packetData->dstMAC = macToString(eth_header.ether_dhost);

    // 检查是否为IP数据包
    if (ntohs(eth_header.ether_type) == ETHERTYPE_IP) {
        return parseIPHeader(packet, sizeof(struct ether_header), packetData);
    }

    // 其他类型的数据包
    packetData->protocol = "OTHER";
    return true;
}

string CSourceTasker::macToString(const u_char* mac)
{
    stringstream ss;
    ss << hex << setfill('0');
    for (int i = 0; i < 6; i++) {
        if (i > 0) ss << ":";
        ss << setw(2) << (int)mac[i];
    }
    return ss.str();
}

string CSourceTasker::ipToString(uint32_t ip)
{
    struct in_addr addr;
    addr.s_addr = ip;
    return string(inet_ntoa(addr));
}

bool CSourceTasker::parseIPHeader(const u_char* packet, int offset, NetworkPacketData* packetData)
{
    // 使用memcpy安全地读取IP头部，避免内存对齐问题
    struct ip ip_header;
    memcpy(&ip_header, packet + offset, sizeof(struct ip));

    // 解析IP地址
    packetData->srcIP = ipToString(ip_header.ip_src.s_addr);
    packetData->dstIP = ipToString(ip_header.ip_dst.s_addr);

    // 获取IP头长度
    int ip_header_len = ip_header.ip_hl * 4;

    // 根据协议类型解析传输层头
    switch (ip_header.ip_p) {
        case IPPROTO_TCP:
            packetData->protocol = "TCP";
            return parseTCPHeader(packet, offset + ip_header_len, packetData);
        case IPPROTO_UDP:
            packetData->protocol = "UDP";
            return parseUDPHeader(packet, offset + ip_header_len, packetData);
        default:
            packetData->protocol = "OTHER";
            return true;
    }
}

bool CSourceTasker::parseTCPHeader(const u_char* packet, int offset, NetworkPacketData* packetData)
{
    // 使用memcpy安全地读取TCP头部，避免内存对齐问题
    struct tcphdr tcp_header;
    memcpy(&tcp_header, packet + offset, sizeof(struct tcphdr));

    // 解析端口号
    packetData->srcPort = ntohs(tcp_header.th_sport);
    packetData->dstPort = ntohs(tcp_header.th_dport);

    return true;
}

bool CSourceTasker::parseUDPHeader(const u_char* packet, int offset, NetworkPacketData* packetData)
{
    // 使用memcpy安全地读取UDP头部，避免内存对齐问题
    struct udphdr udp_header;
    memcpy(&udp_header, packet + offset, sizeof(struct udphdr));

    // 解析端口号
    packetData->srcPort = ntohs(udp_header.uh_sport);
    packetData->dstPort = ntohs(udp_header.uh_dport);

    return true;
}



// 简单过滤方法：根据协议和端口判断是否保留数据包
bool CSourceTasker::shouldKeepPacket(NetworkPacketData* packet)
{
    if (!packet) {
        return false;
    }

    if (packet->protocol == "TCP") {
        // 保留常见的TCP端口
        return (packet->dstPort == 80 || packet->srcPort == 80 ||      // HTTP
                packet->dstPort == 443 || packet->srcPort == 443 ||     // HTTPS
                packet->dstPort == 8080 || packet->srcPort == 8080 ||   // HTTP备用
                packet->dstPort == 22 || packet->srcPort == 22 ||       // SSH
                packet->dstPort == 21 || packet->srcPort == 21);        // FTP
    } else if (packet->protocol == "UDP") {
        // 保留常见的UDP端口
        return (packet->dstPort == 53 || packet->srcPort == 53 ||      // DNS
                packet->dstPort == 67 || packet->srcPort == 67 ||       // DHCP
                packet->dstPort == 68 || packet->srcPort == 68);        // DHCP
    }

    // 其他协议暂时不过滤
    return true;
}