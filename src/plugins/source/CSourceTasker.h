//
//  CSourceTasker.h
//  pangu
//
//  Created by <PERSON> on 2023/6/30.
//  Redesigned for framework compliance
//

#ifndef CSourceTasker_h
#define CSourceTasker_h

#include "common/common.h"
#include "common/msg.h"
#include "core/ITasker.h"
#include <memory>
#include <chrono>
#include <pcap.h>
#include <netinet/in.h>
#include <netinet/if_ether.h>
#include <netinet/ip.h>
#include <netinet/tcp.h>
#include <netinet/udp.h>
#include <arpa/inet.h>

// 前向声明
class CSourcePlugin;

/**
 * Source任务处理器 - 数据包捕获回调处理器
 *
 * 重新设计原则：
 * 1. 作为回调处理器，被Plugin的线程调用
 * 2. 包含PacketCapture的所有功能（迁移过来）
 * 3. 包含所有业务逻辑（抓包、解析、处理）
 * 4. Plugin创建线程，Tasker作为回调执行业务逻辑
 */
class CSourceTasker : public ITasker
{
public:
    CSourceTasker();
    virtual ~CSourceTasker();

    // ITasker接口实现 - 作为回调处理器
    virtual void* execute(void* msg) override;
    virtual void clear() override;

private:

    // PacketCapture功能（迁移过来）
    pcap_t* m_handle;                   // pcap句柄
    string m_deviceName;                // 网卡设备名
    string m_filterExpression;          // 过滤表达式
    string m_lastError;                 // 最后的错误信息
    bool m_isCapturing;                 // 是否正在抓包

    // 统计信息
    int m_totalPackets;                 // 总捕获包数
    int m_validPackets;                 // 有效包数
    struct CaptureStats {
        int packetsReceived;
        int packetsDropped;
        int packetsTotal;
    } m_stats;

    // PacketCapture功能方法（迁移过来）
    bool initDevice(const string& deviceName = "", const string& filter = "");
    bool startCapture();
    void stopCapture();
    NetworkPacketData* getNextPacket();
    vector<string> getAvailableDevices();
    bool setFilter(const string& filterExpression);

    // 数据包解析方法（从PacketCapture迁移）
    bool parseEthernetHeader(const u_char* packet, NetworkPacketData* packetData);
    bool parseIPHeader(const u_char* packet, int offset, NetworkPacketData* packetData);
    bool parseTCPHeader(const u_char* packet, int offset, NetworkPacketData* packetData);
    bool parseUDPHeader(const u_char* packet, int offset, NetworkPacketData* packetData);
    string macToString(const u_char* mac);
    string ipToString(uint32_t ip);

    // 业务逻辑方法
    bool initializeCapture();
    bool preprocessPacket(NetworkPacketData* packet);
    void cleanup();

    // 配置管理
    void loadConfiguration();

    // 配置参数（从Plugin移过来）
    int m_captureInterval;         // 捕获间隔(毫秒)
    int m_batchSize;              // 批处理大小

    // 简单过滤方法
    bool shouldKeepPacket(NetworkPacketData* packet);
};

#endif /* CSourceTasker_h */