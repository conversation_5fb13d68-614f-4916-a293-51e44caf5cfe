//
//  CMonitorHandler.cpp
//  pangu
//
//  Created by <PERSON> on 2023/6/30.
//

#include "CMonitorTasker.h"

CMonitorTasker::CMonitorTasker(IPlugin *plugin): m_plugin(plugin)
{
    printf("new CMonitorTasker \n");
    (void)m_plugin;
}

CMonitorTasker::~CMonitorTasker()
{
    printf("delete CMonitorTasker  \n");
}

void* CMonitorTasker::execute(void *msg)
{
    // 在新的设计中，遥测数据通过同步调用 telemetry() 获取，
    // monitor 的 tasker 不再处理消息队列中的遥测消息。
    // 此方法保留为空以满足编译要求。
    if(msg)
    {
        // 假设收到的任何消息都应该被删除
        delete (char*)msg;
    }
    usleep(1000000);
    return nullptr;
}

void CMonitorTasker::clear()
{
    return;
}

