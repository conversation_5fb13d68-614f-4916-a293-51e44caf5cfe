//
//  CMonitorPlugin.hpp
//  pangoo
//
//  Created by <PERSON> on 2023/11/6.
//

#ifndef CMonitorPlugin_hpp
#define CMonitorPlugin_hpp

#include "common/common.h"
#include "common/msg.h"
#include "core/IPlugin.h"
#include "CMonitorTasker.h"
#include <memory>

class CMonitorPlugin : public IPlugin
{
public:
    CMonitorPlugin();
    virtual ~CMonitorPlugin();
    virtual PluginInfo* info() override;

    // 实现新的 control 和 telemetry 接口
    virtual void control(int msgID, const json& controlData) override;
    virtual json telemetry() override;

    virtual void init() override;
    virtual void uninit() override;
    virtual void start() override;
    virtual void stop() override;

    // 设置其他插件的引用（用于调用 telemetry）- 现在通过dispatcher获取，保留接口兼容性
    void set_plugins(const map<string, IPlugin*>& plugins) { m_plugins = plugins; }

    // 设置分发器插件
    void set_dispatcher(IPlugin* dispatcher) { m_dispatcher = dispatcher; }

    // 定时任务相关
    void start_telemetry_collector();
    void collect_telemetry_data();

private:
    bool m_running;
    pthread_t m_telemetry_thread;
    map<string, IPlugin*> m_plugins;
    map<string, json> m_telemetry_data; // 缓存的遥测数据现在是JSON
    IPlugin* m_dispatcher;

    static void* telemetry_collector_thread(void* arg);
};

#endif /* DemoMonitor_hpp */
