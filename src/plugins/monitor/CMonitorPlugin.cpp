//
//  Monitor.cpp
//  pangu
//
//  Created by <PERSON> on 2023/11/6.
//

#include "CMonitorPlugin.h"
#include <ctime>
#include <cstdlib>

CMonitorPlugin::CMonitorPlugin()
{
    m_info = new PluginInfo("./config/monitor.json");
    m_running = false;
    m_telemetry_thread = 0;
    m_dispatcher = nullptr;
}

CMonitorPlugin::~CMonitorPlugin()
{
    printf("free CMonitorPlugin \n");
    stop();
    delete m_info;
}

PluginInfo* CMonitorPlugin::info()
{
    return m_info;
}

// 实现新的 control 接口
void CMonitorPlugin::control(int msgID, const json& controlData)
{
    LOG_INFO("Monitor 插件收到控制消息 (msgID: " << msgID << "): " << controlData.dump());

    try {
        string type = controlData.value("type", "");

        if (type == "set") {
            // 设置监控器参数
            if (controlData.contains("collection_interval")) {
                int interval = controlData["collection_interval"].get<int>();
                if (interval > 0 && interval <= 300) { // 限制在1-300秒之间
                    LOG_INFO("Monitor: 设置遥测收集间隔为 " << interval << " 秒");
                    // 注意：这里需要重启收集线程才能生效，暂时只记录
                } else {
                    LOG_WARN("Monitor: 无效的收集间隔: " << interval << " (应在1-300秒之间)");
                }
            }

            if (controlData.contains("clear_cache")) {
                bool clear = controlData["clear_cache"].get<bool>();
                if (clear) {
                    m_telemetry_data.clear();
                    LOG_INFO("Monitor: 遥测数据缓存已清空");
                }
            }
        } else if (type == "get") {
            string target = controlData.value("target", "");
            if (target == "status") {
                LOG_INFO("Monitor: 当前状态 - 运行状态: " << (m_running ? "运行中" : "已停止")
                        << ", 缓存报告数: " << m_telemetry_data.size());
            } else if (target == "cached_data") {
                LOG_INFO("Monitor: 返回缓存的遥测数据");
                for (const auto& item : m_telemetry_data) {
                    LOG_INFO("  - " << item.first << ": " << item.second.dump());
                }
            } else if (target == "collect_now") {
                LOG_INFO("Monitor: 立即执行一次遥测数据收集");
                collect_telemetry_data();
            }
        } else {
            LOG_WARN("Monitor: 未知的控制类型: " << type);
        }
    } catch (const json::exception& e) {
        LOG_ERROR("Monitor: 解析控制消息JSON失败: " << e.what());
    }
}

// 实现新的 telemetry 接口
json CMonitorPlugin::telemetry()
{
    return {
        {"plugin_name", "monitor"},
        {"status", m_running ? "running" : "stopped"},
        {"cached_reports", m_telemetry_data.size()},
        {"collection_thread_active", m_telemetry_thread != 0}
    };
}

void CMonitorPlugin::init()
{
    printf("CMonitorPlugin::init() - 初始化监控插件\n");
    m_running = false;
}

void CMonitorPlugin::uninit()
{
    printf("CMonitorPlugin::uninit() - 清理监控插件\n");
    stop();
}

void CMonitorPlugin::start()
{
    m_running = true;
    IPlugin::start();

    // 启动遥测数据收集线程
    pthread_create(&m_telemetry_thread, NULL, telemetry_collector_thread, this);
    printf("Monitor: 遥测数据收集线程已启动\n");
}

void CMonitorPlugin::stop()
{
    if (m_running) {
        printf("CMonitorPlugin::stop() - 停止监控插件\n");
        m_running = false;
        if (m_telemetry_thread) {
            pthread_join(m_telemetry_thread, NULL);
            m_telemetry_thread = 0;
        }
        IPlugin::stop();
    }
}

// 启动遥测数据收集线程
void CMonitorPlugin::start_telemetry_collector()
{
    pthread_create(&m_telemetry_thread, NULL, telemetry_collector_thread, this);
}

// 遥测收集线程函数
void* CMonitorPlugin::telemetry_collector_thread(void* arg)
{
    CMonitorPlugin* monitor = (CMonitorPlugin*)arg;
    while (monitor->m_running) {
        sleep(15); // 每15秒收集一次遥测数据
        if (!monitor->m_running) break;
        monitor->collect_telemetry_data();
    }
    printf("Monitor: 遥测收集线程退出\n");
    return nullptr;
}

// 收集遥测数据
void CMonitorPlugin::collect_telemetry_data()
{
    printf("\n--- Monitor: 开始收集所有插件的遥测数据 ---\n");

    if (m_dispatcher) {
        // 通过dispatcher收集所有插件的telemetry信息
        try {
            json all_telemetry = m_dispatcher->telemetry();

            // 缓存遥测数据
            m_telemetry_data["system"] = all_telemetry;

            // 打印汇总信息
            if (all_telemetry.contains("plugins")) {
                printf("  收集到 %zu 个插件的遥测数据:\n", all_telemetry["plugins"].size());
                for (const auto& plugin_data : all_telemetry["plugins"].items()) {
                    const string& plugin_name = plugin_data.key();
                    const json& data = plugin_data.value();
                    string status = data.contains("status") ? data["status"].get<string>() : "unknown";
                    printf("    - %s: %s\n", plugin_name.c_str(), status.c_str());
                }
            }

            // 打印dispatcher信息
            if (all_telemetry.contains("dispatcher")) {
                const json& dispatcher_data = all_telemetry["dispatcher"];
                printf("  Dispatcher状态: 管理 %d 个插件, 路由表大小 %d\n",
                       dispatcher_data.value("managed_plugins", 0),
                       dispatcher_data.value("routing_table_size", 0));
            }

        } catch (const std::exception& e) {
            printf("  错误: 收集遥测数据失败 - %s\n", e.what());
        }
    } else {
        printf("  警告: Dispatcher未设置，无法收集遥测数据\n");
    }

    printf("--- Monitor: 遥测数据收集完成 ---\n");
}
