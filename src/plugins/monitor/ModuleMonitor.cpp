//
//  ModuleMonitor.cpp
//  pangu
//
//  Created by <PERSON> on 2023/11/6.
//

#include "common/common.h"
#include "CMonitorPlugin.h"

extern "C" __attribute__((visibility("default"))) int load(IPlugin **p, int size)
{
  if (p == NULL || size <= 0)
  {
    return -1;
  }

  p[0] = new CMonitorPlugin();

  return 1;
}

extern "C" __attribute__((visibility("default"))) void unload(IPlugin **p, int size)
{
  for (int i = 0; i < size; i++)
  {
    delete p[i];
  }

  return;
}