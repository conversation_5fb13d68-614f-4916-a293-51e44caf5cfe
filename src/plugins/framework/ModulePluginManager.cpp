//
//  ModuleTestPlugin.cpp
//  pangoo
//
//  Created by <PERSON> on 2023/11/6.
//

#include "common/common.h"
#include "PluginManager.h"

extern "C" __attribute__((visibility("default"))) int load(IPlugin **p, [[maybe_unused]] int size)
{
    if (p == NULL)
    {
        return -1;
    }
    
    *p = new PluginManager();
    
    return 1;
}

extern "C" __attribute__((visibility("default"))) void unload(IPlugin *p, [[maybe_unused]] int size)
{
    delete p;
    
    return;
}