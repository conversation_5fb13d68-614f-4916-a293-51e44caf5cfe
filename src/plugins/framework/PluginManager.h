//
//  PluginManager.hpp
//  pangoo
//
//  Created by <PERSON> on 2023/6/8.
//

#ifndef PluginManager_hpp
#define PluginManager_hpp

#include "common/common.h"
#include "common/msg.h"
#include "core/IPlugin.h"
#include "core/Queue.hpp"

typedef int (*load_t)(IPlugin **p, int size);
typedef int (*unload_t)(IPlugin **p, int size);

using namespace std;

typedef vector<string> Plugins;

class PluginManager: public IPlugin
{
public:
    PluginManager();
    virtual ~PluginManager();

    virtual void init() override;
    virtual void uninit() override {}
    virtual void start() override;
    virtual void stop() override;

    virtual PluginInfo* info() override;
    // handle方法已移除 - Tasker会直接将消息传递给下游的队列
    // control 和 telemetry 现在有默认实现，不需要重写

private:
    void loadPlugins();
    void unloadPlugins();
    void initPlugins();
    void initDI();
    void initBus();

    // 插件管理相关
    PluginInfo* m_info;
    std::map<std::string, IPlugin*> m_plugins;
};

#endif /* PluginManager_hpp */