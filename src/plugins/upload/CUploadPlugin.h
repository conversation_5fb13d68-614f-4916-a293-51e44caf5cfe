//
//  DemoUpload.hpp
//  pangoo
//
//  Created by <PERSON> on 2023/11/6.
//

#ifndef DemoUpload_hpp
#define DemoUpload_hpp

#include "common/common.h"
#include "common/msg.h"
#include "core/IPlugin.h"
#include "CUploadTasker.h"
#include <memory>
#include <atomic>

class CUploadPlugin : public IPlugin
{
public:
    CUploadPlugin();
    virtual ~CUploadPlugin();
    virtual PluginInfo* info() override;

    // 实现新的 control 和 telemetry 接口
    virtual void control(int msgID, const json& controlData) override;
    virtual json telemetry() override;

    virtual void init() override;
    virtual void uninit() override {}
    virtual void stop() override {}

private:
    // 统计信息
    std::atomic<uint64_t> m_uploaded_events{0};
    std::atomic<uint64_t> m_upload_errors{0};
    std::atomic<uint64_t> m_shared_memory_writes{0};
    std::atomic<bool> m_upload_enabled{true};
};


#endif /* DemoUpload_hpp */