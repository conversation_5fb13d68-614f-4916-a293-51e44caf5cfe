//
//  CUploadTasker.h
//  pangoo
//
//  Created by <PERSON> on 2023/6/30.
//

#ifndef C_Upload_Tasker_h
#define C_Upload_Tasker_h

#include "common/common.h"
#include "common/msg.h"
#include "utils/utils.h"
#include "core/ITasker.h"
#include "core/IPlugin.h"
#include "core/ShmQueue.hpp"
#include "3rd/json/json.hpp"
#include <chrono>
#include <iomanip>
#include <sstream>
#include <memory>

using namespace std;
using json = nlohmann::json;

class CUploadTasker : public ITasker
{
public:
    CUploadTasker();
    virtual ~CUploadTasker();
    virtual void* execute(void *msg) override;
    virtual void clear() override;

private:
    // 共享内存队列
    ShmQueue* m_shared_queue;

    // 创建HTTP上传事件的JSON格式
    json createHTTPUploadEvent(const HTTPParseResult* httpResult);

    // 写入数据到共享内存
    bool writeToSharedMemory(const json& data);

    // 辅助方法
    string generateEventId();
    string getCurrentTimeString();
};

#endif /* C_Upload_Tasker_h */