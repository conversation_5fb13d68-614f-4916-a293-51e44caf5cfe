//
//  DemoUpload.cpp
//  pangu
//
//  Created by <PERSON> on 2023/11/6.
//

#include "CUploadPlugin.h"

CUploadPlugin::CUploadPlugin()
{
    m_info = new PluginInfo("./config/upload.json");
}

CUploadPlugin::~CUploadPlugin()
{
    // printf("delete CPlugin \n");
    delete m_info;
}

PluginInfo* CUploadPlugin::info()
{
    return m_info;
}

// 实现新的 control 接口
void CUploadPlugin::control(int msgID, const json& controlData)
{
    LOG_INFO("Upload 插件收到控制消息 (msgID: " << msgID << "): " << controlData.dump());

    try {
        string type = controlData.value("type", "");

        if (type == "set") {
            // 设置上传器参数
            if (controlData.contains("upload_enabled")) {
                bool enabled = controlData["upload_enabled"].get<bool>();
                m_upload_enabled = enabled;
                LOG_INFO("Upload: 设置上传功能为 " << (enabled ? "启用" : "禁用"));
            }

            if (controlData.contains("reset_stats")) {
                bool reset = controlData["reset_stats"].get<bool>();
                if (reset) {
                    m_uploaded_events = 0;
                    m_upload_errors = 0;
                    m_shared_memory_writes = 0;
                    LOG_INFO("Upload: 统计信息已重置");
                }
            }
        } else if (type == "get") {
            string target = controlData.value("target", "");
            if (target == "status") {
                LOG_INFO("Upload: 当前状态 - 已上传事件: " << m_uploaded_events.load()
                        << ", 上传错误: " << m_upload_errors.load()
                        << ", 共享内存写入: " << m_shared_memory_writes.load()
                        << ", 上传功能: " << (m_upload_enabled.load() ? "启用" : "禁用"));
            } else if (target == "stats") {
                LOG_INFO("Upload: 详细统计信息请求");
            }
        } else {
            LOG_WARN("Upload: 未知的控制类型: " << type);
        }
    } catch (const json::exception& e) {
        LOG_ERROR("Upload: 解析控制消息JSON失败: " << e.what());
    }
}

// 实现新的 telemetry 接口
json CUploadPlugin::telemetry()
{
    return {
        {"plugin_name", "upload"},
        {"status", "running"},
        {"uploaded_events", m_uploaded_events.load()},
        {"upload_errors", m_upload_errors.load()},
        {"shared_memory_writes", m_shared_memory_writes.load()},
        {"upload_enabled", m_upload_enabled.load()},
        {"worker_count", info()->_workerNum}
    };
}

void CUploadPlugin::init()
{
    // 创建 Tasker（在 initDI() 之前创建，这样 setup() 时会自动注入）
    m_tasker = new CUploadTasker();
}