//
//  CUploadTasker.cpp
//  pangu
//
//  Created by <PERSON> on 2023/6/30.
//

#include "CUploadTasker.h"

CUploadTasker::CUploadTasker()
{
    try {
        // 初始化共享内存队列（作为生产者）
        m_shared_queue = new ShmQueue("/pangu_upload_queue", true);
        printf("CUploadTasker: 共享内存队列初始化成功\n");
    } catch (const std::exception& e) {
        printf("CUploadTasker: 共享内存队列初始化失败: %s\n", e.what());
        m_shared_queue = nullptr;
    }
}

CUploadTasker::~CUploadTasker()
{
    if (m_shared_queue) {
        delete m_shared_queue;
        m_shared_queue = nullptr;
    }
    // printf("delete CUploadTasker  \n");
}

void* CUploadTasker::execute(void *msg)
{
    if(!msg)
    {
        usleep(1000000);
        return nullptr;
    }

    // 尝试识别消息类型
    // 首先检查是否为HTTP解析结果（msgID 22）
    HTTPParseResult* httpResult = (HTTPParseResult*)msg;
    bool isHTTPParseResult = false;

    try {
        // 更安全的类型检查：检查指针有效性和isValid字段
        if (httpResult &&
            (uintptr_t)httpResult > 0x1000 &&  // 基本指针有效性检查
            httpResult->isValid == true) {
            isHTTPParseResult = true;
        }
    } catch (...) {
        isHTTPParseResult = false;
    }

    if (isHTTPParseResult) {
        // 创建JSON格式的上传事件
        json uploadEvent = createHTTPUploadEvent(httpResult);

        printf("CUploadHandler: 接收到HTTP解析结果开始上传\n");
        printf("上传事件JSON: %s\n", uploadEvent.dump(2).c_str());

        // 写入共享内存
        if (writeToSharedMemory(uploadEvent)) {
            printf("CUploadHandler: 数据已写入共享内存\n");
        } else {
            printf("CUploadHandler: 写入共享内存失败\n");
        }

        // 模拟HTTP解析结果上传处理
        usleep(50000);  // 50ms上传时间
        printf("CUploadHandler: HTTP解析结果上传完成\n");

        // 这里可以添加实际的上传逻辑，比如：
        // - 发送到远程服务器
        // - 保存到文件
        // - 发送到数据库等
        // - 发送JSON到消息队列等

        delete httpResult;
        return nullptr;  // Upload是最后环节，不需要返回消息
    }

    // 已删除：无效的ParserToUploadData处理代码
    // 现在只处理HTTPParseResult类型的消息

    return nullptr;  // Upload是最后环节，不需要返回消息
}

void CUploadTasker::clear()
{
    return;
}

json CUploadTasker::createHTTPUploadEvent(const HTTPParseResult* httpResult)
{
    json event;

    // 事件基本信息
    event["event_type"] = "http_request";
    event["event_id"] = generateEventId();
    event["timestamp"] = httpResult->timestamp;
    event["capture_time"] = getCurrentTimeString();

    // HTTP请求信息
    event["http"] = {
        {"method", httpResult->method},
        {"url", httpResult->url},
        {"path", httpResult->path},
        {"query", httpResult->query},
        {"version", httpResult->httpVersion},
        {"status_code", httpResult->statusCode},
        {"status_message", httpResult->statusMessage},
        {"content_length", httpResult->contentLength},
        {"is_complete", httpResult->isComplete}
    };

    // 网络连接信息
    event["connection"] = {
        {"src_ip", httpResult->srcIP},
        {"src_port", httpResult->srcPort},
        {"dst_ip", httpResult->dstIP},
        {"dst_port", httpResult->dstPort},
        {"protocol", "TCP"}
    };

    // HTTP头部信息（如果有的话）
    if (!httpResult->headers.empty()) {
        event["http"]["headers"] = httpResult->headers;
    }

    // HTTP消息体信息（如果有的话）
    if (!httpResult->body.empty()) {
        event["http"]["body"] = httpResult->body;
        event["http"]["body_size"] = httpResult->body.length();
    }

    // 处理状态
    event["processing"] = {
        {"parsed_by", "pangu_parser"},
        {"uploaded_by", "pangu_uploader"},
        {"is_valid", httpResult->isValid}
    };

    return event;
}

string CUploadTasker::generateEventId()
{
    // 生成简单的事件ID（时间戳 + 随机数）
    auto now = std::chrono::system_clock::now();
    auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
    int random_num = rand() % 10000;
    return "evt_" + std::to_string(timestamp) + "_" + std::to_string(random_num);
}

string CUploadTasker::getCurrentTimeString()
{
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;

    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    ss << '.' << std::setfill('0') << std::setw(3) << ms.count();
    return ss.str();
}

bool CUploadTasker::writeToSharedMemory(const json& data)
{
    if (!m_shared_queue) {
        return false;
    }

    try {
        std::string json_str = data.dump();
        return m_shared_queue->push(json_str);
    } catch (const std::exception& e) {
        printf("CUploadTasker: 写入共享内存异常: %s\n", e.what());
        return false;
    }
}