#include "CInteractorTasker.h"
#include "../dispatcher/CDispatcherPlugin.h"
#include "3rd/json/json.hpp"
#include <unistd.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <errno.h>
#include <poll.h>

using json = nlohmann::json;

CInteractorTasker::CInteractorTasker(CDispatcherPlugin* dispatcher) :
    m_pipe_path("/tmp/pangoo_ipc.pipe"),
    m_pipe_fd(-1),
    m_dispatcher(dispatcher)
{
    unlink(m_pipe_path.c_str());
    if (mkfifo(m_pipe_path.c_str(), 0666) == -1) {
        perror("mkfifo");
        LOG_ERROR("创建命名管道失败: " << m_pipe_path);
    }
    open_pipe();
}

CInteractorTasker::~CInteractorTasker()
{
    close_pipe();
    unlink(m_pipe_path.c_str());
}

void CInteractorTasker::open_pipe()
{
    if (m_pipe_fd == -1) {
        m_pipe_fd = open(m_pipe_path.c_str(), O_RDONLY | O_NONBLOCK);
        if (m_pipe_fd == -1) {
            if (errno != ENXIO) {
                perror("open pipe");
                LOG_ERROR("InteractorTasker: Failed to open pipe: " << m_pipe_path);
            }
        }
    }
}

void CInteractorTasker::close_pipe()
{
    if (m_pipe_fd != -1) {
        close(m_pipe_fd);
        m_pipe_fd = -1;
    }
}

void* CInteractorTasker::execute(void *msg)
{
    (void)msg;

    if (m_pipe_fd == -1) {
        open_pipe();
        if (m_pipe_fd == -1) {
            return nullptr;
        }
    }

    char buffer[4096];
    struct pollfd pfd;
    pfd.fd = m_pipe_fd;
    pfd.events = POLLIN;

    int poll_ret = poll(&pfd, 1, 50);

    if (poll_ret > 0 && (pfd.revents & POLLIN)) {
        ssize_t bytes_read = read(m_pipe_fd, buffer, sizeof(buffer) - 1);

        if (bytes_read > 0) {
            buffer[bytes_read] = '\0';
            LOG_INFO("Interactor: 收到命令 " << buffer);

            json cmd_json = json::parse(buffer, nullptr, false);
            if (!cmd_json.is_discarded() && cmd_json.is_object() &&
                cmd_json.contains("msgID") && cmd_json.contains("data")) {

                if (m_dispatcher) {
                    int msgID = cmd_json["msgID"];
                    json data = cmd_json["data"];
                    m_dispatcher->control(msgID, data);
                    LOG_INFO("Interactor: 命令已分发 (msgID: " << msgID << ")");
                }
            } else {
                LOG_ERROR("InteractorTasker: JSON格式无效或缺少必需字段");
            }
        } else if (bytes_read == 0) {
            close_pipe();
        } else if (errno != EAGAIN && errno != EWOULDBLOCK) {
            LOG_ERROR("InteractorTasker: 管道读取错误");
            close_pipe();
        }
    } else if (poll_ret < 0) {
        LOG_ERROR("InteractorTasker: poll错误");
        close_pipe();
    }

    return nullptr;
}

void CInteractorTasker::clear()
{
    return;
}
