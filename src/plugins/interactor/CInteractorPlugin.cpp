//
//  CInteractorPlugin.cpp
//  pangu
//
//  Created by <PERSON> on 2023/11/6.
//

#include "CInteractorPlugin.h"
#include "../dispatcher/CDispatcherPlugin.h"
#include <unistd.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <chrono>
#include <thread>

CInteractorPlugin::CInteractorPlugin() :
    m_dispatcher(nullptr),
    m_running(false),
    m_should_exit(false),
    m_tasker(nullptr)
{
    m_info = new PluginInfo("./config/interactor.json");
}

CInteractorPlugin::~CInteractorPlugin()
{
    m_should_exit = true;
    if (m_tasker_thread.joinable()) {
        m_tasker_thread.join();
    }
    delete m_info;
    if (m_tasker) {
        delete m_tasker;
        m_tasker = nullptr;
    }
}

PluginInfo* CInteractorPlugin::info()
{
    return m_info;
}

void CInteractorPlugin::init()
{
    m_running = false;
    // 在init阶段只初始化状态，tasker的创建延迟到dispatcher设置后
}



void CInteractorPlugin::uninit()
{
    stop();
}

void CInteractorPlugin::start()
{
    LOG_INFO("CInteractorPlugin: 启动交互插件");

    // 如果dispatcher已设置但tasker还未创建，现在创建
    if (m_dispatcher && !m_tasker) {
        m_tasker = new CInteractorTasker(m_dispatcher);
        // 创建线程
        m_tasker_thread = std::thread(&CInteractorPlugin::task_thread_entry_point, this);
        LOG_INFO("CInteractorPlugin: Dispatcher已设置，tasker和线程已创建");
    }

    m_running = true;
}

void CInteractorPlugin::stop()
{
    LOG_INFO("CInteractorPlugin: 停止交互插件");
    m_running = false;
}

void CInteractorPlugin::task_thread_entry_point()
{
    if (!m_tasker) {
        LOG_ERROR("CInteractorPlugin: Tasker is null in thread entry point.");
        return;
    }

    LOG_INFO("CInteractorPlugin: 线程已创建，开始运行");

    // 线程始终运行，但只在m_running为true时处理任务
    while (!m_should_exit) {
        if (m_running) {
            m_tasker->execute(nullptr);
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        } else {
            // 当停止时，休眠更长时间以减少CPU占用
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }

    LOG_INFO("CInteractorPlugin: 线程退出");
}
