#ifndef C_Interactor_Tasker_h
#define C_Interactor_Tasker_h

#include "common/common.h"
#include "common/msg.h"
#include "core/ITasker.h"
#include "core/IPlugin.h"
#include <atomic>
#include <string>

using namespace std;

// 前向声明
class CDispatcherPlugin;

class CInteractorTasker : public ITasker
{
public:
    CInteractorTasker(CDispatcherPlugin* dispatcher);
    virtual ~CInteractorTasker();
    virtual void* execute(void *msg) override;
    virtual void clear() override;

private:
    std::string m_pipe_path;
    int m_pipe_fd;
    CDispatcherPlugin* m_dispatcher;

    void open_pipe();
    void close_pipe();
};

#endif /* C_Interactor_Tasker_h */