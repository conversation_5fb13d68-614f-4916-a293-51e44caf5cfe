//
//  CInteractorPlugin.hpp
//  pangu
//
//  Created by <PERSON> on 2023/11/6.
//

#ifndef CInteractorPlugin_hpp
#define CInteractorPlugin_hpp

#include <string>
#include <atomic>
#include <thread>

#include "common/common.h"
#include "common/msg.h"
#include "core/IPlugin.h"
#include "CInteractorTasker.h"

// 前向声明
class CDispatcherPlugin;

class CInteractorPlugin : public IPlugin
{
public:
    CInteractorPlugin();
    virtual ~CInteractorPlugin();
    virtual PluginInfo* info() override;

    virtual void init() override;
    virtual void uninit() override;
    virtual void start() override;
    virtual void stop() override;

    void set_dispatcher(CDispatcherPlugin* dispatcher) { m_dispatcher = dispatcher; }

private:
    CDispatcherPlugin* m_dispatcher;
    std::thread m_tasker_thread;
    std::atomic<bool> m_running;
    std::atomic<bool> m_should_exit;
    CInteractorTasker* m_tasker;

    void task_thread_entry_point();
};

#endif /* CInteractorPlugin_hpp */