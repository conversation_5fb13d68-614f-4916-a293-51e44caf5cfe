//
//  CDispatcherPlugin.cpp
//  pangu
//
//  Created by <PERSON> on 2023/11/6.
//

#include "CDispatcherPlugin.h"
#include <chrono>
#include <ctime>

CDispatcherPlugin::CDispatcherPlugin()
{
    m_info = new PluginInfo("./config/dispatcher.json");
}

CDispatcherPlugin::~CDispatcherPlugin()
{
    printf("delete CDispatcherPlugin \n");
    delete m_info;
}

PluginInfo* CDispatcherPlugin::info()
{
    return m_info;
}

// 新增：构建从 msgID 到插件列表的路由表
void CDispatcherPlugin::buildRoutingTable()
{
    printf("Dispatcher: Building control message routing table...\n");
    m_routing_table.clear();

    // 遍历由 PluginManager 注入的所有插件
    for (auto const& [plugin_name, plugin_ptr] : m_plugin_map)
    {
        if (plugin_ptr)
        {
            PluginInfo* p_info = plugin_ptr->info();
            // 遍历该插件订阅的控制消息ID列表
            for (const auto& msgID : p_info->_controlflow_receive)
            {
                // 将插件添加到对应 msgID 的订阅列表中
                m_routing_table[msgID].push_back(plugin_ptr);
                printf("  - Routing msgID %d to plugin '%s'\n", msgID, plugin_name.c_str());
            }
        }
    }
    printf("Dispatcher: Routing table build complete.\n");
}

void CDispatcherPlugin::init()
{
    // 基于从 PluginManager 获取的插件列表，构建路由表
    buildRoutingTable();

    // Dispatcher插件不需要Tasker，因为它通过直接调用control方法进行分发
    // 不需要通过队列处理消息
    // m_tasker 保持为 nullptr，这样Worker即使启动也不会处理消息

    printf("Dispatcher Plugin initialized (no tasker needed).\n");
}

void CDispatcherPlugin::start()
{
    printf("Dispatcher Plugin started (no worker available).\n");
}

void CDispatcherPlugin::stop()
{
    // Dispatcher插件没有启动Worker线程，所以也不需要停止
    // 注意：不调用 IPlugin::stop() 保持与start()的一致性
    printf("Dispatcher Plugin stopped (no worker to stop).\n");
}

// 实现同步控制接口
void CDispatcherPlugin::control(int msgID, const json& data)
{
    // 查找路由表，找到订阅此msgID的插件
    auto it = m_routing_table.find(msgID);
    if (it != m_routing_table.end()) {
        // 遍历所有订阅此msgID的插件，调用它们的control方法
        for (IPlugin* plugin : it->second) {
            if (plugin) {
                plugin->control(msgID, data);
            }
        }
        printf("Dispatcher: 控制消息 msgID %d 已分发给 %zu 个插件\n",
               msgID, it->second.size());
    } else {
        printf("Dispatcher: 警告 - 没有插件订阅 msgID %d\n", msgID);
    }
}

// 实现telemetry接口 - 收集所有插件的telemetry信息
json CDispatcherPlugin::telemetry()
{
    json all_telemetry;
    all_telemetry["dispatcher"] = {
        {"plugin_name", "dispatcher"},
        {"status", "running"},
        {"routing_table_size", m_routing_table.size()},
        {"managed_plugins", m_plugin_map.size()}
    };

    // 收集所有插件的telemetry信息
    for (const auto& pair : m_plugin_map) {
        const string& plugin_name = pair.first;
        IPlugin* plugin = pair.second;

        if (plugin && plugin != this) { // 避免递归调用自己
            try {
                json plugin_telemetry = plugin->telemetry();
                all_telemetry["plugins"][plugin_name] = plugin_telemetry;
            } catch (const std::exception& e) {
                all_telemetry["plugins"][plugin_name] = {
                    {"plugin_name", plugin_name},
                    {"status", "error"},
                    {"error", e.what()}
                };
            }
        }
    }

    // 添加时间戳
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    all_telemetry["timestamp"] = time_t;
    all_telemetry["collection_time"] = std::ctime(&time_t);

    return all_telemetry;
}
