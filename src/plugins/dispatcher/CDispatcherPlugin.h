//
//  CDispatcherPlugin.hpp
//  pangu
//
//  Created by <PERSON> on 2023/11/6.
//

#ifndef CDispatcherPlugin_hpp
#define CDispatcherPlugin_hpp

#include "common/common.h"
#include "common/msg.h"
#include "core/IPlugin.h"
#include <memory>

class CDispatcherPlugin : public IPlugin
{
public:
    CDispatcherPlugin();
    virtual ~CDispatcherPlugin();
    virtual PluginInfo* info() override;

    // 重写telemetry接口 - 收集所有插件的telemetry信息
    virtual json telemetry() override;

    virtual void init() override;
    virtual void uninit() override {}
    virtual void start() override;
    virtual void stop() override;

    // 从 PluginManager 接收所有插件的列表
    void set_plugins(const map<string, IPlugin*>& plugins) { m_plugin_map = plugins; }

    // 重写control接口 - 供InteractorTasker直接调用
    virtual void control(int msgID, const json& data) override;

private:
    // 存储从 PluginManager 注入的所有插件
    map<string, IPlugin*> m_plugin_map;
    
    // 新增：用于构建路由表
    map<int, list<IPlugin*>> m_routing_table;
    void buildRoutingTable();
};

#endif /* DemoDispatcher_hpp */
