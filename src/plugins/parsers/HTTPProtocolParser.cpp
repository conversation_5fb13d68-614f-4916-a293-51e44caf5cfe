//
//  HTTPProtocolParser.cpp
//  pangoo
//
//  Created by <PERSON> on 2023/11/6.
//

#include "HTTPProtocolParser.h"
#include <sstream>
#include <algorithm>

HTTPProtocolParser::HTTPProtocolParser() {
    // printf("HTTPProtocolParser: 初始化HTTP协议解析器\n");
    resetStats();
}

HTTPProtocolParser::~HTTPProtocolParser() {
    // printf("HTTPProtocolParser: 清理HTTP协议解析器\n");
}

bool HTTPProtocolParser::isHTTPTraffic(const string& data) {
    if (data.empty() || data.size() < 4) {
        return false;
    }
    
    // 检查HTTP请求方法
    string upperData = data.substr(0, min(data.size(), (size_t)20));
    transform(upperData.begin(), upperData.end(), upperData.begin(), ::toupper);
    
    if (upperData.find("GET ") == 0 || upperData.find("POST ") == 0 ||
        upperData.find("PUT ") == 0 || upperData.find("DELETE ") == 0 ||
        upperData.find("HEAD ") == 0 || upperData.find("OPTIONS ") == 0 ||
        upperData.find("PATCH ") == 0 || upperData.find("TRACE ") == 0 ||
        upperData.find("CONNECT ") == 0) {
        return true;
    }
    
    // 检查HTTP响应
    if (upperData.find("HTTP/") == 0) {
        return true;
    }
    
    return false;
}

vector<HTTPMessage> HTTPProtocolParser::parseHTTPStream(const TCPConnection& conn, const string& data) {
    vector<HTTPMessage> messages;
    
    if (!isHTTPTraffic(data)) {
        return messages;
    }
    
    // 分割可能包含多个HTTP消息的数据流
    vector<string> httpMessages = splitHTTPMessages(data);
    
    for (const string& msgData : httpMessages) {
        if (msgData.empty()) continue;
        
        HTTPMessage message;
        message.connection = conn;
        message.timestamp = (int)time(nullptr);
        
        // 判断是请求还是响应
        if (msgData.find("HTTP/") == 0) {
            // HTTP响应
            message = parseHTTPResponse(conn, msgData);
            if (message.isValid) {
                m_stats.totalResponses++;
                m_stats.statusCounts[message.statusCode]++;
            }
        } else {
            // HTTP请求
            message = parseHTTPRequest(conn, msgData);
            if (message.isValid) {
                m_stats.totalRequests++;
                m_stats.methodCounts[message.methodStr]++;
            }
        }
        
        if (message.isValid) {
            m_stats.validMessages++;
            messages.push_back(message);
            
            printf("HTTP: %s %s\n",
                   message.type == HTTP_REQUEST ? message.methodStr.c_str() : to_string(message.statusCode).c_str(),
                   message.type == HTTP_REQUEST ? message.url.c_str() : message.statusMessage.c_str());
        } else {
            m_stats.invalidMessages++;
        }
    }
    
    return messages;
}

HTTPMessage HTTPProtocolParser::parseHTTPRequest(const TCPConnection& conn, const string& data) {
    HTTPMessage message;
    message.type = HTTP_REQUEST;
    message.connection = conn;
    message.timestamp = (int)time(nullptr);
    
    // 分离头部和消息体
    size_t headerEndPos = data.find("\r\n\r\n");
    if (headerEndPos == string::npos) {
        headerEndPos = data.find("\n\n");
        if (headerEndPos == string::npos) {
            message.isValid = false;
            return message;
        }
    }
    
    string headerSection = data.substr(0, headerEndPos);
    string bodySection = data.substr(headerEndPos + 4);
    
    // 分离请求行和头部
    size_t firstLineEnd = headerSection.find("\r\n");
    if (firstLineEnd == string::npos) {
        firstLineEnd = headerSection.find("\n");
        if (firstLineEnd == string::npos) {
            message.isValid = false;
            return message;
        }
    }
    
    string requestLine = headerSection.substr(0, firstLineEnd);
    string headersSection = headerSection.substr(firstLineEnd + 1);
    
    // 解析请求行
    if (!parseRequestLine(requestLine, message.method, message.methodStr, 
                         message.url, message.path, message.query, message.httpVersion)) {
        message.isValid = false;
        return message;
    }
    
    // 解析头部
    if (!parseHTTPHeaders(headersSection, message.headers)) {
        message.isValid = false;
        return message;
    }
    
    // 处理消息体
    message.contentLength = getContentLength(message.headers);
    message.body = bodySection;
    
    // 检查消息是否完整
    message.isComplete = isHTTPMessageComplete(data);
    message.isValid = true;
    
    return message;
}

HTTPMessage HTTPProtocolParser::parseHTTPResponse(const TCPConnection& conn, const string& data) {
    HTTPMessage message;
    message.type = HTTP_RESPONSE;
    message.connection = conn;
    message.timestamp = (int)time(nullptr);
    
    // 分离头部和消息体
    size_t headerEndPos = data.find("\r\n\r\n");
    if (headerEndPos == string::npos) {
        headerEndPos = data.find("\n\n");
        if (headerEndPos == string::npos) {
            message.isValid = false;
            return message;
        }
    }
    
    string headerSection = data.substr(0, headerEndPos);
    string bodySection = data.substr(headerEndPos + 4);
    
    // 分离状态行和头部
    size_t firstLineEnd = headerSection.find("\r\n");
    if (firstLineEnd == string::npos) {
        firstLineEnd = headerSection.find("\n");
        if (firstLineEnd == string::npos) {
            message.isValid = false;
            return message;
        }
    }
    
    string statusLine = headerSection.substr(0, firstLineEnd);
    string headersSection = headerSection.substr(firstLineEnd + 1);
    
    // 解析状态行
    if (!parseStatusLine(statusLine, message.httpVersion, message.statusCode, message.statusMessage)) {
        message.isValid = false;
        return message;
    }
    
    // 解析头部
    if (!parseHTTPHeaders(headersSection, message.headers)) {
        message.isValid = false;
        return message;
    }
    
    // 处理消息体
    message.contentLength = getContentLength(message.headers);
    message.body = bodySection;
    
    // 检查消息是否完整
    message.isComplete = isHTTPMessageComplete(data);
    message.isValid = true;
    
    return message;
}

bool HTTPProtocolParser::parseRequestLine(const string& line, HTTPMethod& method, string& methodStr,
                                         string& url, string& path, string& query, string& version) {
    istringstream iss(line);
    if (!(iss >> methodStr >> url >> version)) {
        return false;
    }
    
    method = stringToMethod(methodStr);
    parseURL(url, path, query);
    
    return true;
}

bool HTTPProtocolParser::parseStatusLine(const string& line, string& version, int& statusCode, string& statusMessage) {
    istringstream iss(line);
    if (!(iss >> version >> statusCode)) {
        return false;
    }
    
    // 获取状态消息（可能包含空格）
    size_t statusMsgPos = line.find(' ', line.find(' ') + 1);
    if (statusMsgPos != string::npos) {
        statusMessage = line.substr(statusMsgPos + 1);
    }
    
    return true;
}

bool HTTPProtocolParser::parseHTTPHeaders(const string& headerSection, map<string, string>& headers) {
    istringstream stream(headerSection);
    string line;
    
    while (getline(stream, line)) {
        if (line.empty() || line == "\r") continue;
        
        size_t colonPos = line.find(':');
        if (colonPos == string::npos) continue;
        
        string key = trim(line.substr(0, colonPos));
        string value = trim(line.substr(colonPos + 1));
        
        // 移除可能的\r
        if (!value.empty() && value.back() == '\r') {
            value.pop_back();
        }
        
        headers[toLowerCase(key)] = value;
    }
    
    return true;
}

void HTTPProtocolParser::parseURL(const string& url, string& path, string& query) {
    size_t queryPos = url.find('?');
    if (queryPos != string::npos) {
        path = url.substr(0, queryPos);
        query = url.substr(queryPos + 1);
    } else {
        path = url;
        query = "";
    }
}

HTTPMethod HTTPProtocolParser::stringToMethod(const string& methodStr) {
    string upper = methodStr;
    transform(upper.begin(), upper.end(), upper.begin(), ::toupper);
    
    if (upper == "GET") return HTTP_GET;
    if (upper == "POST") return HTTP_POST;
    if (upper == "PUT") return HTTP_PUT;
    if (upper == "DELETE") return HTTP_DELETE;
    if (upper == "HEAD") return HTTP_HEAD;
    if (upper == "OPTIONS") return HTTP_OPTIONS;
    if (upper == "PATCH") return HTTP_PATCH;
    if (upper == "TRACE") return HTTP_TRACE;
    if (upper == "CONNECT") return HTTP_CONNECT;
    
    return HTTP_METHOD_UNKNOWN;
}

string HTTPProtocolParser::toLowerCase(const string& str) {
    string result = str;
    transform(result.begin(), result.end(), result.begin(), ::tolower);
    return result;
}

string HTTPProtocolParser::trim(const string& str) {
    size_t start = str.find_first_not_of(" \t\r\n");
    if (start == string::npos) return "";
    
    size_t end = str.find_last_not_of(" \t\r\n");
    return str.substr(start, end - start + 1);
}

vector<string> HTTPProtocolParser::splitHTTPMessages(const string& data) {
    vector<string> messages;
    
    // 简单实现：假设每个HTTP消息都以\r\n\r\n结束
    size_t pos = 0;
    while (pos < data.size()) {
        size_t endPos = data.find("\r\n\r\n", pos);
        if (endPos == string::npos) {
            // 最后一个消息或不完整的消息
            messages.push_back(data.substr(pos));
            break;
        }
        
        messages.push_back(data.substr(pos, endPos - pos + 4));
        pos = endPos + 4;
    }
    
    return messages;
}

bool HTTPProtocolParser::isHTTPMessageComplete(const string& data) {
    // 简单检查：是否包含完整的头部
    return data.find("\r\n\r\n") != string::npos || data.find("\n\n") != string::npos;
}

size_t HTTPProtocolParser::getContentLength(const map<string, string>& headers) {
    auto it = headers.find("content-length");
    if (it != headers.end()) {
        try {
            return stoul(it->second);
        } catch (...) {
            return 0;
        }
    }
    return 0;
}

bool HTTPProtocolParser::isChunkedEncoding(const map<string, string>& headers) {
    auto it = headers.find("transfer-encoding");
    if (it != headers.end()) {
        return toLowerCase(it->second).find("chunked") != string::npos;
    }
    return false;
}
