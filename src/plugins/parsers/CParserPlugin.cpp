//
//  Parser.cpp
//  pangoo
//
//  Created by <PERSON> on 2023/11/7.
//

#include "CParserPlugin.h"

CParserPlugin::CParserPlugin()
{
    m_info = new PluginInfo("./config/parser.json");
}

CParserPlugin::~CParserPlugin()
{
    printf("delete CPlugin \n");
    delete m_info;
}

PluginInfo* CParserPlugin::info()
{
    return m_info;
}

// 实现新的 control 接口
void CParserPlugin::control(int msgID, const json& controlData)
{
    LOG_INFO("Parser 插件收到控制消息 (msgID: " << msgID << "): " << controlData.dump());

    try {
        string type = controlData.value("type", "");

        if (type == "set") {
            // 设置解析器参数
            if (controlData.contains("debug_mode")) {
                bool debug = controlData["debug_mode"].get<bool>();
                m_debug_mode = debug;
                LOG_INFO("Parser: 设置调试模式为 " << (debug ? "开启" : "关闭"));
            }

            if (controlData.contains("reset_stats")) {
                bool reset = controlData["reset_stats"].get<bool>();
                if (reset) {
                    m_processed_packets = 0;
                    m_parsed_http_requests = 0;
                    m_parsing_errors = 0;
                    LOG_INFO("Parser: 统计信息已重置");
                }
            }
        } else if (type == "get") {
            string target = controlData.value("target", "");
            if (target == "status") {
                LOG_INFO("Parser: 当前状态 - 已处理数据包: " << m_processed_packets.load()
                        << ", HTTP请求: " << m_parsed_http_requests.load()
                        << ", 解析错误: " << m_parsing_errors.load()
                        << ", 调试模式: " << (m_debug_mode.load() ? "开启" : "关闭"));
            } else if (target == "stats") {
                LOG_INFO("Parser: 详细统计信息请求");
            }
        } else {
            LOG_WARN("Parser: 未知的控制类型: " << type);
        }
    } catch (const json::exception& e) {
        LOG_ERROR("Parser: 解析控制消息JSON失败: " << e.what());
    }
}

// 实现新的 telemetry 接口
json CParserPlugin::telemetry()
{
    return {
        {"plugin_name", "parser"},
        {"status", "running"},
        {"processed_packets", m_processed_packets.load()},
        {"parsed_http_requests", m_parsed_http_requests.load()},
        {"parsing_errors", m_parsing_errors.load()},
        {"debug_mode", m_debug_mode.load()},
        {"worker_count", info()->_workerNum}
    };
}

void CParserPlugin::init()
{
    // 创建 Tasker（在 initDI() 之前创建，这样 setup() 时会自动注入）
    m_tasker = new CParserTasker();
}
