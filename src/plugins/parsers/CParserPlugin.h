//
//  CParserPlugin.hpp
//  pangoo
//
//  Created by <PERSON> on 2023/11/7.
//

#ifndef CParserPlugin_hpp
#define CParserPlugin_hpp

#include "common/common.h"
#include "common/msg.h"
#include "core/IPlugin.h"
#include "CParserTasker.h"
#include <memory>
#include <atomic>

class CParserPlugin : public IPlugin
{
public:
    CParserPlugin();
    virtual ~CParserPlugin();
    virtual PluginInfo* info() override;

    // 实现新的 control 和 telemetry 接口
    virtual void control(int msgID, const json& controlData) override;
    virtual json telemetry() override;

    virtual void init() override;
    virtual void uninit() override {}
    // start() 和 stop() 方法使用父类 IPlugin 的默认实现

private:
    // 统计信息
    std::atomic<uint64_t> m_processed_packets{0};
    std::atomic<uint64_t> m_parsed_http_requests{0};
    std::atomic<uint64_t> m_parsing_errors{0};
    std::atomic<bool> m_debug_mode{false};
};

#endif /* DemoParser_hpp */