//
//  CParserTasker.h
//  pangoo
//
//  Created by <PERSON> on 2023/6/30.
//

#ifndef C_Parser_Tasker_h
#define C_Parser_Tasker_h

#include "common/common.h"
#include "common/msg.h"
#include "utils/utils.h"
#include "core/ITasker.h"
#include "core/IPlugin.h"
#include "HTTPProtocolParser.h"
#include "3rd/json/json.hpp"

using namespace std;
using json = nlohmann::json;

class CParserTasker : public ITasker
{
public:
    CParserTasker();
    virtual ~CParserTasker();
    virtual void* execute(void *msg) override;
    virtual void clear() override;

private:
    HTTPProtocolParser* m_httpParser;

    // 处理网络数据包，返回解析结果（如果有的话）
    HTTPParseResult* processNetworkPacket(const NetworkPacketData* networkData);

    // 注意：processTCPStreams方法已删除（从未被使用）

    // 将HTTP消息转换为解析结果
    HTTPParseResult* convertToParseResult(const HTTPMessage& httpMsg);

    // 将map转换为JSON字符串
    string mapToJsonString(const map<string, string>& headers);

    // 简化的HTTP检测和解析函数
    bool isSimpleHTTPRequest(const string& data);
    bool extractHTTPInfo(const string& data, const string& dstIP, int dstPort,
                        string& method, string& url, string& fullUrl);
    HTTPParseResult* createSimpleHTTPResult(const NetworkPacketData* networkData,
                                           const string& method, const string& url, const string& fullUrl);
};

#endif /* C_Parser_Tasker_h */