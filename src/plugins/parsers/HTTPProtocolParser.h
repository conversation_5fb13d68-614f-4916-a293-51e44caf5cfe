//
//  HTTPProtocolParser.h
//  pangoo
//
//  Created by <PERSON> on 2023/11/6.
//

#ifndef HTTPProtocolParser_h
#define HTTPProtocolParser_h

#include "common/common.h"
#include <map>
#include <vector>
#include <regex>

using namespace std;

/**
 * 简化的TCP连接四元组（从TCPSessionManager迁移过来）
 */
struct TCPConnection {
    string srcIP;
    int srcPort;
    string dstIP;
    int dstPort;

    // 重载比较操作符，用于map的key
    bool operator<(const TCPConnection& other) const {
        if (srcIP != other.srcIP) return srcIP < other.srcIP;
        if (srcPort != other.srcPort) return srcPort < other.srcPort;
        if (dstIP != other.dstIP) return dstIP < other.dstIP;
        return dstPort < other.dstPort;
    }

    // 获取反向连接（用于双向会话）
    TCPConnection reverse() const {
        return {dstIP, dstPort, srcIP, srcPort};
    }
};

/**
 * HTTP消息类型
 */
enum HTTPMessageType {
    HTTP_REQUEST = 0,
    HTTP_RESPONSE = 1,
    HTTP_UNKNOWN = 2
};

/**
 * HTTP请求方法
 */
enum HTTPMethod {
    HTTP_GET = 0,
    HTTP_POST,
    HTTP_PUT,
    HTTP_DELETE,
    HTTP_HEAD,
    HTTP_OPTIONS,
    HTTP_PATCH,
    HTTP_TRACE,
    HTTP_CONNECT,
    HTTP_METHOD_UNKNOWN
};

/**
 * HTTP消息结构
 */
struct HTTPMessage {
    HTTPMessageType type;
    TCPConnection connection;
    int timestamp;
    
    // 请求相关字段
    HTTPMethod method;
    string methodStr;
    string url;
    string path;
    string query;
    string httpVersion;
    
    // 响应相关字段
    int statusCode;
    string statusMessage;
    
    // 通用字段
    map<string, string> headers;
    string body;
    size_t contentLength;
    
    // 元数据
    bool isComplete;
    bool isValid;
    
    HTTPMessage() : type(HTTP_UNKNOWN), timestamp(0), method(HTTP_METHOD_UNKNOWN),
                   statusCode(0), contentLength(0), isComplete(false), isValid(false) {}
};

/**
 * HTTP协议解析器
 * 负责解析HTTP请求和响应消息
 */
class HTTPProtocolParser {
public:
    HTTPProtocolParser();
    ~HTTPProtocolParser();
    
    // 解析HTTP数据流
    vector<HTTPMessage> parseHTTPStream(const TCPConnection& conn, const string& data);
    
    // 检测是否为HTTP流量
    bool isHTTPTraffic(const string& data);
    
    // 获取解析统计信息
    struct ParseStats {
        int totalRequests;
        int totalResponses;
        int validMessages;
        int invalidMessages;
        map<string, int> methodCounts;
        map<int, int> statusCounts;
    };
    ParseStats getStats() const { return m_stats; }
    
    // 重置统计信息
    void resetStats() { m_stats = ParseStats(); }

private:
    ParseStats m_stats;
    
    // 解析HTTP请求
    HTTPMessage parseHTTPRequest(const TCPConnection& conn, const string& data);
    
    // 解析HTTP响应
    HTTPMessage parseHTTPResponse(const TCPConnection& conn, const string& data);
    
    // 解析HTTP头部
    bool parseHTTPHeaders(const string& headerSection, map<string, string>& headers);
    
    // 解析请求行
    bool parseRequestLine(const string& line, HTTPMethod& method, string& methodStr, 
                         string& url, string& path, string& query, string& version);
    
    // 解析状态行
    bool parseStatusLine(const string& line, string& version, int& statusCode, string& statusMessage);
    
    // 解析URL
    void parseURL(const string& url, string& path, string& query);
    
    // HTTP方法字符串转枚举
    HTTPMethod stringToMethod(const string& methodStr);
    
    // 字符串转小写
    string toLowerCase(const string& str);
    
    // 去除字符串首尾空白
    string trim(const string& str);
    
    // 分割HTTP消息（处理多个HTTP消息在同一个TCP流中的情况）
    vector<string> splitHTTPMessages(const string& data);
    
    // 检查HTTP消息是否完整
    bool isHTTPMessageComplete(const string& data);
    
    // 获取Content-Length
    size_t getContentLength(const map<string, string>& headers);
    
    // 检查是否为分块传输编码
    bool isChunkedEncoding(const map<string, string>& headers);
};

#endif /* HTTPProtocolParser_h */
