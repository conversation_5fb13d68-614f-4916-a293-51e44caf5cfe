//
//  main.cpp
//  pangu
//
//  Created by <PERSON> on 2023/6/7.
//

#include "common/common.h"
#include "core/IPlugin.h"


typedef int (*load_t)(IPlugin **p, int size);
typedef int (*unload_t)(IPlugin *p, int size);

int main()
{
    void *handle = dlopen("./plugins/plugin_manager.so", RTLD_NOW);
    if(!handle)
    {
        printf("plugin_manager.so load failed: %s\n", dlerror());
        return 0;
    }
    void *load = dlsym(handle, "load");
    void *unload = dlsym(handle, "unload");
    
    IPlugin *p;
    int n = 0;

    n = (*(load_t)load)(&p, sizeof(p));
    (void)n;  // 避免未使用变量警告
    
    p->init();
    p->start();
    
    while(1)
    {
        sleep(2);
    }
    
    n = (*(unload_t)unload)(p, sizeof(p));
    return 0;
}
