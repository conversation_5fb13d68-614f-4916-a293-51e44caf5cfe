//
//  msg.h
//  pangoo
//
//  Created by <PERSON> on 2023/6/7.
//

#ifndef msg_h
#define msg_h

#include "common/common.h"
#include "3rd/json/json.hpp"
#include <set>
#include <map>
#include <mutex>

using namespace std;
using json = nlohmann::json;

// ========== 数据流消息 ==========

/**
 * 网络数据包消息结构 (ID=11)
 */
struct NetworkPacketData
{
    void* packetData;
    size_t packetSize;
    int timestamp;
    string srcIP, dstIP, protocol, srcMAC, dstMAC, interface;
    int srcPort, dstPort;
    NetworkPacketData(void* data = nullptr, size_t size = 0, const string& iface = "")
        : packetData(data), packetSize(size), timestamp((int)time(nullptr)),
          interface(iface), srcPort(0), dstPort(0), ownsData(true), isDestroyed(false) {}
    NetworkPacketD<PERSON>(const NetworkPacketData&) = delete;
    NetworkPacketData& operator=(const NetworkPacketData&) = delete;
    ~NetworkPacketData() {
        if (isDestroyed) return;
        isDestroyed = true;
        if (packetData && ownsData) { free(packetData); packetData = nullptr; }
        ownsData = false;
    }
private:
    bool ownsData, isDestroyed;
};

/**
 * HTTP解析结果消息结构 (ID=21)
 */
struct HTTPParseResult
{
    string srcIP, dstIP, method, url, path, query, httpVersion, statusMessage, headers, body;
    int srcPort, dstPort, timestamp, messageType, statusCode;
    size_t contentLength;
    bool isComplete, isValid;
    HTTPParseResult()
        : srcPort(0), dstPort(0), timestamp((int)time(nullptr)), messageType(0),
          statusCode(0), contentLength(0), isComplete(false), isValid(false) {}
};


// ========== 消息ID定义 ==========
enum DataMsgID {
    MSG_DATA_SOURCE_TO_PARSER = 11,
    MSG_DATA_PARSER_TO_UPLOAD = 21,
};

enum ControlMsgID {
    MSG_CONTROL_SOURCE = 301,
    MSG_CONTROL_PARSER = 302,
    MSG_CONTROL_UPLOAD = 303,
    MSG_CONTROL_ALL    = 399, // 广播给所有插件
};

enum TelemetryMsgID {
    MSG_TELEMETRY_REQUEST = 201, // 请求遥测数据
};

enum MonitorMsgID {
    MSG_MONITOR_COLLECT = 201,   // 收集遥测数据
    MSG_MONITOR_REPORT = 202,    // 生成监控报告
    MSG_MONITOR_ALERT = 203,     // 监控告警
};


// ========== 消息名称映射 ==========

/**
 * @brief 消息ID到消息名称的静态映射
 *
 * 提供一个全局可访问的、从消息ID到其可读名称的映射。
 * 这对于日志记录、调试和监控非常有用，可以快速识别消息类型。
 */
static const std::map<int, std::string> MessageNames = {
    // ----- 数据流消息 (Data Flow Messages) -----
    {MSG_DATA_SOURCE_TO_PARSER, "NetworkPacketData: 网络数据包"},
    {MSG_DATA_PARSER_TO_UPLOAD, "HTTPParseResult: HTTP解析结果"},

    // ----- 遥测消息 (Telemetry Messages) -----
    {MSG_TELEMETRY_REQUEST, "TelemetryRequest: 请求遥测数据"},

    // ----- 控制消息 (Control Messages) -----
    {MSG_CONTROL_SOURCE, "ControlSource: 控制Source插件"},
    {MSG_CONTROL_PARSER, "ControlParser: 控制Parser插件"},
    {MSG_CONTROL_UPLOAD, "ControlUpload: 控制Upload插件"},
    {MSG_CONTROL_ALL,    "ControlAll: 广播控制消息"},

    // ----- 监控消息 (Monitor Messages) -----
    {MSG_MONITOR_COLLECT, "MonitorCollect: 收集遥测数据"},
    {MSG_MONITOR_REPORT, "MonitorReport: 生成监控报告"},
    {MSG_MONITOR_ALERT, "MonitorAlert: 监控告警"}
};


#endif /* msg_h */