//
//  common.hpp
//  pangu
//
//  Created by <PERSON> on 2023/11/9.
//

#ifndef common_hpp
#define common_hpp

#include <stdio.h>
#include <unistd.h>
#include <pthread.h>
#include <thread>
#include <chrono>
#include <string>
#include <map>
#include <vector>
#include <fstream>
#include <iostream>
#include <list>
#include <dirent.h>
#include <sys/stat.h> 
#include <string.h>
#include <dlfcn.h>

using namespace std;

// 简单的日志宏，避免printf格式化问题
#define LOG_INFO(msg) std::cout << "[INFO] " << msg << std::endl
#define LOG_WARN(msg) std::cout << "[WARN] " << msg << std::endl
#define LOG_ERROR(msg) std::cout << "[ERROR] " << msg << std::endl

#endif /* common_hpp */
